"""
Unit tests for dashboard_app views.

This module contains comprehensive unit tests for all view functions in the dashboard_app,
including customer, provider, and admin dashboard views.
"""

# --- Standard Library Imports ---
import json
from datetime import timedelta
from decimal import Decimal
from unittest.mock import Mock, patch

# --- Third-Party Imports ---
from django.contrib.auth import get_user_model
from django.contrib.messages import get_messages
from django.http import JsonResponse
from django.test import Client, TestCase, override_settings
from django.urls import reverse
from django.utils import timezone

# --- Local App Imports ---
from booking_cart_app.models import Booking, BookingItem
from dashboard_app.models import FavoriteVenue
from venues_app.models import Service, Venue
from accounts_app.models import CustomUser, TeamMember
from accounts_app.models.customer import CustomerProfile
from accounts_app.models.provider import ServiceProviderProfile

User = get_user_model()


@override_settings(SECURE_SSL_REDIRECT=False)
class CustomerDashboardViewTest(TestCase):
    """Test the customer dashboard view functionality."""

    def setUp(self):
        """Set up test data."""
        self.client = Client()
        
        # Create customer user
        self.customer = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.customer.role = CustomUser.CUSTOMER
        self.customer.save()
        
        # Create customer profile
        self.customer_profile = CustomerProfile.objects.create(user=self.customer)

        # Create service provider user
        self.provider = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.provider.role = CustomUser.SERVICE_PROVIDER
        self.provider.save()
        
        # Create service provider profile
        self.provider_profile = ServiceProviderProfile.objects.create(
            user=self.provider,
            legal_name='Test Spa',
            phone='+**********',
            contact_name='John Doe',
            address='123 Test Street',
            city='Test City',
            state='NY',
            zip_code='12345'
        )

        # Create venue
        self.venue = Venue.objects.create(
            service_provider=self.provider_profile,
            venue_name='Test Spa Venue',
            short_description='A test spa venue',
            street_number='123',
            street_name='Test Street',
            city='Test City',
            state='Test State',
            county='Test County',
            approval_status='approved',
            visibility='active'
        )

        # Create service
        self.service = Service.objects.create(
            venue=self.venue,
            service_title='Test Massage',
            short_description='A relaxing massage',
            price_min=Decimal('100.00'),
            duration_minutes=60,
            is_active=True
        )

        # URL for customer dashboard
        self.dashboard_url = reverse('dashboard_app:customer_dashboard')

    def test_customer_dashboard_access_authenticated_customer(self):
        """Test that authenticated customers can access the dashboard."""
        self.client.login(email='<EMAIL>', password='testpass123')
        response = self.client.get(self.dashboard_url)
        
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dashboard_app/customer/dashboard.html')
        self.assertContains(response, 'Customer Dashboard')

    def test_customer_dashboard_access_unauthenticated(self):
        """Test that unauthenticated users are redirected to login."""
        response = self.client.get(self.dashboard_url)
        self.assertEqual(response.status_code, 302)
        self.assertIn('/accounts/customer/login/', response.url)

    def test_customer_dashboard_access_non_customer(self):
        """Test that non-customers are redirected with error message."""
        self.client.login(email='<EMAIL>', password='testpass123')
        response = self.client.get(self.dashboard_url)
        
        self.assertEqual(response.status_code, 302)
        self.assertRedirects(response, reverse('venues_app:home'))
        
        # Check error message
        messages = list(get_messages(response.wsgi_request))
        self.assertTrue(any('Only customers can access this dashboard' in str(message) for message in messages))

    def test_customer_dashboard_context_data(self):
        """Test that dashboard context contains expected data."""
        self.client.login(email='<EMAIL>', password='testpass123')
        
        # Create some test bookings
        booking = Booking.objects.create(
            customer=self.customer,
            venue=self.venue,
            total_price=Decimal('100.00'),
            status='confirmed'
        )
        
        # Create favorite venue
        favorite = FavoriteVenue.objects.create(
            customer=self.customer,
            venue=self.venue
        )
        
        response = self.client.get(self.dashboard_url)
        
        self.assertEqual(response.status_code, 200)
        
        # Check context data
        context = response.context
        self.assertIn('customer_profile', context)
        self.assertIn('recent_bookings', context)
        self.assertIn('upcoming_bookings', context)
        self.assertIn('favorite_venues_count', context)
        self.assertIn('total_bookings', context)
        self.assertIn('pending_bookings', context)
        self.assertIn('confirmed_bookings', context)
        
        # Check specific values
        self.assertEqual(context['favorite_venues_count'], 1)
        self.assertEqual(context['total_bookings'], 1)
        self.assertEqual(context['confirmed_bookings'], 1)

    @patch('dashboard_app.views.customer.log_dashboard_access')
    def test_customer_dashboard_logging(self, mock_log_access):
        """Test that dashboard access is properly logged."""
        self.client.login(email='<EMAIL>', password='testpass123')
        response = self.client.get(self.dashboard_url)

        self.assertEqual(response.status_code, 200)
        mock_log_access.assert_called_once()

    @patch('dashboard_app.views.customer.log_error')
    def test_customer_dashboard_error_handling(self, mock_log_error):
        """Test error handling in customer dashboard."""
        self.client.login(email='<EMAIL>', password='testpass123')

        # Mock an exception in the view
        with patch('dashboard_app.views.customer.CustomerProfile.objects.get_or_create', side_effect=Exception('Test error')):
            response = self.client.get(self.dashboard_url)

            self.assertEqual(response.status_code, 302)
            self.assertRedirects(response, reverse('venues_app:home'))
            mock_log_error.assert_called_once()


@override_settings(SECURE_SSL_REDIRECT=False)
class CustomerBookingStatusViewTest(TestCase):
    """Test the customer booking status view functionality."""

    def setUp(self):
        """Set up test data."""
        self.client = Client()
        
        # Create customer user
        self.customer = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.customer.role = CustomUser.CUSTOMER
        self.customer.save()
        
        # Create customer profile
        self.customer_profile = CustomerProfile.objects.create(user=self.customer)

        # Create service provider user
        self.provider = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.provider.role = CustomUser.SERVICE_PROVIDER
        self.provider.save()
        
        # Create service provider profile
        self.provider_profile = ServiceProviderProfile.objects.create(
            user=self.provider,
            legal_name='Test Spa',
            phone='+**********',
            contact_name='John Doe',
            address='123 Test Street',
            city='Test City',
            state='NY',
            zip_code='12345'
        )

        # Create venue
        self.venue = Venue.objects.create(
            service_provider=self.provider_profile,
            venue_name='Test Spa Venue',
            short_description='A test spa venue',
            state='Test State',
            county='Test County',
            city='Test City',
            street_number='123',
            street_name='Test Street',
            approval_status='approved',
            visibility='active'
        )

        # URL for booking status
        self.booking_status_url = reverse('dashboard_app:customer_booking_status')

    def test_booking_status_access_authenticated_customer(self):
        """Test that authenticated customers can access booking status."""
        self.client.login(email='<EMAIL>', password='testpass123')
        response = self.client.get(self.booking_status_url)
        
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dashboard_app/customer/booking_status.html')

    def test_booking_status_access_non_customer(self):
        """Test that non-customers are redirected with error message."""
        self.client.login(email='<EMAIL>', password='testpass123')
        response = self.client.get(self.booking_status_url)
        
        self.assertEqual(response.status_code, 302)
        self.assertRedirects(response, reverse('venues_app:home'))

    def test_booking_status_filtering_by_status(self):
        """Test filtering bookings by status."""
        self.client.login(email='<EMAIL>', password='testpass123')
        
        # Create bookings with different statuses
        pending_booking = Booking.objects.create(
            customer=self.customer,
            venue=self.venue,
            total_price=Decimal('100.00'),
            status='pending'
        )
        confirmed_booking = Booking.objects.create(
            customer=self.customer,
            venue=self.venue,
            total_price=Decimal('150.00'),
            status='confirmed'
        )
        
        # Test filtering by pending status
        response = self.client.get(self.booking_status_url, {'status': 'pending'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'pending')
        
        # Test filtering by confirmed status
        response = self.client.get(self.booking_status_url, {'status': 'confirmed'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'confirmed')

    def test_booking_status_filtering_by_date(self):
        """Test filtering bookings by date."""
        self.client.login(email='<EMAIL>', password='testpass123')
        
        # Create bookings with different dates
        today = timezone.now().date()
        past_date = today - timedelta(days=7)
        future_date = today + timedelta(days=7)
        
        past_booking = Booking.objects.create(
            customer=self.customer,
            venue=self.venue,
            total_price=Decimal('100.00'),
            status='completed',
            booking_date=timezone.make_aware(timezone.datetime.combine(past_date, timezone.datetime.min.time()))
        )
        future_booking = Booking.objects.create(
            customer=self.customer,
            venue=self.venue,
            total_price=Decimal('150.00'),
            status='confirmed',
            booking_date=timezone.make_aware(timezone.datetime.combine(future_date, timezone.datetime.min.time()))
        )
        
        # Test filtering by upcoming
        response = self.client.get(self.booking_status_url, {'date': 'upcoming'})
        self.assertEqual(response.status_code, 200)
        
        # Test filtering by past
        response = self.client.get(self.booking_status_url, {'date': 'past'})
        self.assertEqual(response.status_code, 200)

    def test_booking_status_pagination(self):
        """Test pagination in booking status view."""
        self.client.login(email='<EMAIL>', password='testpass123')
        
        # Create more than 10 bookings to test pagination
        for i in range(15):
            Booking.objects.create(
                customer=self.customer,
                venue=self.venue,
                total_price=Decimal('100.00'),
                status='confirmed'
            )
        
        response = self.client.get(self.booking_status_url)
        self.assertEqual(response.status_code, 200)
        
        # Check pagination
        self.assertIn('page_obj', response.context)
        self.assertTrue(response.context['page_obj'].has_next())

    @patch('dashboard_app.views.customer.log_booking_status_access')
    def test_booking_status_logging(self, mock_log_access):
        """Test that booking status access is properly logged."""
        self.client.login(email='<EMAIL>', password='testpass123')
        response = self.client.get(self.booking_status_url)

        self.assertEqual(response.status_code, 200)
        mock_log_access.assert_called_once()


@override_settings(SECURE_SSL_REDIRECT=False)
class CustomerProfileEditViewTest(TestCase):
    """Test the customer profile edit view functionality."""

    def setUp(self):
        """Set up test data."""
        self.client = Client()

        # Create customer user
        self.customer = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.customer.role = CustomUser.CUSTOMER
        self.customer.save()

        # Create customer profile
        self.customer_profile = CustomerProfile.objects.create(user=self.customer)

        # Create non-customer user
        self.provider = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.provider.role = CustomUser.SERVICE_PROVIDER
        self.provider.save()

        # URL for profile edit
        self.profile_edit_url = reverse('dashboard_app:customer_profile_edit')

    def test_profile_edit_redirect_authenticated_customer(self):
        """Test that authenticated customers are redirected to accounts app profile edit."""
        self.client.login(email='<EMAIL>', password='testpass123')
        response = self.client.get(self.profile_edit_url)

        self.assertEqual(response.status_code, 302)
        self.assertRedirects(response, reverse('accounts_app:customer_profile_edit'))

    def test_profile_edit_access_non_customer(self):
        """Test that non-customers are redirected with error message."""
        self.client.login(email='<EMAIL>', password='testpass123')
        response = self.client.get(self.profile_edit_url)

        self.assertEqual(response.status_code, 302)
        self.assertRedirects(response, reverse('venues_app:home'))

    @patch('dashboard_app.views.customer.log_profile_access')
    def test_profile_edit_logging(self, mock_log_access):
        """Test that profile edit access is properly logged."""
        self.client.login(email='<EMAIL>', password='testpass123')
        response = self.client.get(self.profile_edit_url)

        self.assertEqual(response.status_code, 302)
        mock_log_access.assert_called_once()


@override_settings(SECURE_SSL_REDIRECT=False)
class CustomerFavoriteVenuesViewTest(TestCase):
    """Test the customer favorite venues view functionality."""

    def setUp(self):
        """Set up test data."""
        self.client = Client()

        # Create customer user
        self.customer = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.customer.role = CustomUser.CUSTOMER
        self.customer.save()

        # Create customer profile
        self.customer_profile = CustomerProfile.objects.create(user=self.customer)

        # Create service provider user
        self.provider = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.provider.role = CustomUser.SERVICE_PROVIDER
        self.provider.save()

        # Create service provider profile
        self.provider_profile = ServiceProviderProfile.objects.create(
            user=self.provider,
            legal_name='Test Spa',
            phone='+**********',
            contact_name='John Doe',
            address='123 Test Street',
            city='Test City',
            state='NY',
            zip_code='12345'
        )

        # Create venue
        self.venue = Venue.objects.create(
            service_provider=self.provider_profile,
            venue_name='Test Spa Venue',
            short_description='A test spa venue',
            state='Test State',
            county='Test County',
            city='Test City',
            street_number='123',
            street_name='Test Street',
            approval_status='approved',
            visibility='active'
        )

        # URL for favorite venues
        self.favorite_venues_url = reverse('dashboard_app:customer_favorite_venues')

    def test_favorite_venues_access_authenticated_customer(self):
        """Test that authenticated customers can access favorite venues."""
        self.client.login(email='<EMAIL>', password='testpass123')
        response = self.client.get(self.favorite_venues_url)

        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dashboard_app/customer/favorite_venues.html')

    def test_favorite_venues_access_non_customer(self):
        """Test that non-customers are redirected with error message."""
        self.client.login(email='<EMAIL>', password='testpass123')
        response = self.client.get(self.favorite_venues_url)

        self.assertEqual(response.status_code, 302)
        self.assertRedirects(response, reverse('venues_app:home'))

    def test_favorite_venues_context_data(self):
        """Test that favorite venues context contains expected data."""
        self.client.login(email='<EMAIL>', password='testpass123')

        # Create favorite venues
        favorite = FavoriteVenue.objects.create(
            customer=self.customer,
            venue=self.venue
        )

        response = self.client.get(self.favorite_venues_url)

        self.assertEqual(response.status_code, 200)

        # Check context data
        context = response.context
        self.assertIn('page_obj', context)
        self.assertIn('favorite_venues', context)
        self.assertIn('total_favorites', context)

        # Check specific values
        self.assertEqual(context['total_favorites'], 1)

    def test_favorite_venues_pagination(self):
        """Test pagination in favorite venues view."""
        self.client.login(email='<EMAIL>', password='testpass123')

        # Create multiple venues and favorites
        for i in range(15):
            # Create a unique service provider for each venue
            provider = User.objects.create_user(
                email=f'provider{i}@example.com',
                password='testpass123'
            )
            provider.role = CustomUser.SERVICE_PROVIDER
            provider.save()

            provider_profile = ServiceProviderProfile.objects.create(
                user=provider,
                legal_name=f'Test Spa {i}',
                phone=f'+123456789{i}',
                contact_name=f'John Doe {i}',
                address=f'{i} Test Street',
                city='Test City',
                state='NY',
                zip_code='12345'
            )

            venue = Venue.objects.create(
                service_provider=provider_profile,
                venue_name=f'Test Venue {i}',
                short_description=f'Test venue {i}',
                state='Test State',
                county='Test County',
                city='Test City',
                street_number=str(i),
                street_name='Test Street',
                approval_status='approved',
                visibility='active'
            )
            FavoriteVenue.objects.create(
                customer=self.customer,
                venue=venue
            )

        response = self.client.get(self.favorite_venues_url)
        self.assertEqual(response.status_code, 200)

        # Check pagination
        self.assertIn('page_obj', response.context)
        self.assertTrue(response.context['page_obj'].has_next())

    @patch('dashboard_app.views.customer.log_dashboard_activity')
    def test_favorite_venues_logging(self, mock_log_activity):
        """Test that favorite venues access is properly logged."""
        self.client.login(email='<EMAIL>', password='testpass123')
        response = self.client.get(self.favorite_venues_url)

        self.assertEqual(response.status_code, 200)
        mock_log_activity.assert_called_once()


@override_settings(SECURE_SSL_REDIRECT=False)
class AddFavoriteVenueViewTest(TestCase):
    """Test the add favorite venue AJAX view functionality."""

    def setUp(self):
        """Set up test data."""
        self.client = Client()

        # Create customer user
        self.customer = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.customer.role = CustomUser.CUSTOMER
        self.customer.save()

        # Create customer profile
        self.customer_profile = CustomerProfile.objects.create(user=self.customer)

        # Create service provider user
        self.provider = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.provider.role = CustomUser.SERVICE_PROVIDER
        self.provider.save()

        # Create service provider profile
        self.provider_profile = ServiceProviderProfile.objects.create(
            user=self.provider,
            legal_name='Test Spa',
            phone='+**********',
            contact_name='John Doe',
            address='123 Test Street',
            city='Test City',
            state='NY',
            zip_code='12345'
        )

        # Create venue
        self.venue = Venue.objects.create(
            service_provider=self.provider_profile,
            venue_name='Test Spa Venue',
            short_description='A test spa venue',
            state='Test State',
            county='Test County',
            city='Test City',
            street_number='123',
            street_name='Test Street',
            approval_status='approved',
            visibility='active'
        )

    def test_add_favorite_venue_success(self):
        """Test successfully adding a venue to favorites."""
        self.client.login(email='<EMAIL>', password='testpass123')

        url = reverse('dashboard_app:add_favorite_venue', kwargs={'venue_id': self.venue.id})
        response = self.client.post(url)

        self.assertEqual(response.status_code, 200)

        # Parse JSON response
        data = json.loads(response.content)
        self.assertTrue(data['success'])
        self.assertEqual(data['action'], 'added')
        self.assertIn('added to your favorites', data['message'])

        # Check that favorite was created
        self.assertTrue(FavoriteVenue.objects.filter(
            customer=self.customer,
            venue=self.venue
        ).exists())

    def test_add_favorite_venue_already_exists(self):
        """Test adding a venue that's already in favorites."""
        self.client.login(email='<EMAIL>', password='testpass123')

        # Create existing favorite
        FavoriteVenue.objects.create(
            customer=self.customer,
            venue=self.venue
        )

        url = reverse('dashboard_app:add_favorite_venue', kwargs={'venue_id': self.venue.id})
        response = self.client.post(url)

        self.assertEqual(response.status_code, 200)

        # Parse JSON response
        data = json.loads(response.content)
        self.assertFalse(data['success'])
        self.assertEqual(data['action'], 'exists')
        self.assertIn('already in your favorites', data['message'])

    def test_add_favorite_venue_non_customer(self):
        """Test that non-customers cannot add favorites."""
        self.client.login(email='<EMAIL>', password='testpass123')

        url = reverse('dashboard_app:add_favorite_venue', kwargs={'venue_id': self.venue.id})
        response = self.client.post(url, HTTP_X_REQUESTED_WITH='XMLHttpRequest')

        self.assertEqual(response.status_code, 403)

        # Parse JSON response
        data = json.loads(response.content)
        self.assertFalse(data['success'])
        self.assertIn('Only customers can access', data['message'])

    def test_add_favorite_venue_not_found(self):
        """Test adding a non-existent venue to favorites."""
        self.client.login(email='<EMAIL>', password='testpass123')

        url = reverse('dashboard_app:add_favorite_venue', kwargs={'venue_id': 99999})
        response = self.client.post(url)

        self.assertEqual(response.status_code, 404)

        # Parse JSON response
        data = json.loads(response.content)
        self.assertFalse(data['success'])
        self.assertIn('not found', data['message'])

    def test_add_favorite_venue_get_method_not_allowed(self):
        """Test that GET method is not allowed for adding favorites."""
        self.client.login(email='<EMAIL>', password='testpass123')

        url = reverse('dashboard_app:add_favorite_venue', kwargs={'venue_id': self.venue.id})
        response = self.client.get(url)

        self.assertEqual(response.status_code, 405)  # Method not allowed

    @patch('dashboard_app.views.customer.log_favorite_venue_event')
    def test_add_favorite_venue_logging(self, mock_log_event):
        """Test that adding favorite venue is properly logged."""
        self.client.login(email='<EMAIL>', password='testpass123')

        url = reverse('dashboard_app:add_favorite_venue', kwargs={'venue_id': self.venue.id})
        response = self.client.post(url)

        self.assertEqual(response.status_code, 200)
        mock_log_event.assert_called_once()


@override_settings(SECURE_SSL_REDIRECT=False)
class RemoveFavoriteVenueViewTest(TestCase):
    """Test the remove favorite venue AJAX view functionality."""

    def setUp(self):
        """Set up test data."""
        self.client = Client()

        # Create customer user
        self.customer = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.customer.role = CustomUser.CUSTOMER
        self.customer.save()

        # Create customer profile
        self.customer_profile = CustomerProfile.objects.create(user=self.customer)

        # Create service provider user
        self.provider = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.provider.role = CustomUser.SERVICE_PROVIDER
        self.provider.save()

        # Create service provider profile
        self.provider_profile = ServiceProviderProfile.objects.create(
            user=self.provider,
            legal_name='Test Spa',
            phone='+**********',
            contact_name='John Doe',
            address='123 Test Street',
            city='Test City',
            state='NY',
            zip_code='12345'
        )

        # Create venue
        self.venue = Venue.objects.create(
            service_provider=self.provider_profile,
            venue_name='Test Spa Venue',
            short_description='A test spa venue',
            state='Test State',
            county='Test County',
            city='Test City',
            street_number='123',
            street_name='Test Street',
            approval_status='approved',
            visibility='active'
        )

    def test_remove_favorite_venue_success(self):
        """Test successfully removing a venue from favorites."""
        self.client.login(email='<EMAIL>', password='testpass123')

        # Create favorite to remove
        favorite = FavoriteVenue.objects.create(
            customer=self.customer,
            venue=self.venue
        )

        url = reverse('dashboard_app:remove_favorite_venue', kwargs={'venue_id': self.venue.id})
        response = self.client.post(url)

        self.assertEqual(response.status_code, 200)

        # Parse JSON response
        data = json.loads(response.content)
        self.assertTrue(data['success'])
        self.assertEqual(data['action'], 'removed')
        self.assertIn('removed from your favorites', data['message'])

        # Check that favorite was deleted
        self.assertFalse(FavoriteVenue.objects.filter(
            customer=self.customer,
            venue=self.venue
        ).exists())

    def test_remove_favorite_venue_not_in_favorites(self):
        """Test removing a venue that's not in favorites."""
        self.client.login(email='<EMAIL>', password='testpass123')

        url = reverse('dashboard_app:remove_favorite_venue', kwargs={'venue_id': self.venue.id})
        response = self.client.post(url)

        self.assertEqual(response.status_code, 200)

        # Parse JSON response
        data = json.loads(response.content)
        self.assertFalse(data['success'])
        self.assertEqual(data['action'], 'not_found')
        self.assertIn('not in your favorites', data['message'])

    @patch('dashboard_app.views.customer.log_favorite_venue_event')
    def test_remove_favorite_venue_logging(self, mock_log_event):
        """Test that removing favorite venue is properly logged."""
        self.client.login(email='<EMAIL>', password='testpass123')

        # Create favorite to remove
        favorite = FavoriteVenue.objects.create(
            customer=self.customer,
            venue=self.venue
        )

        url = reverse('dashboard_app:remove_favorite_venue', kwargs={'venue_id': self.venue.id})
        response = self.client.post(url)

        self.assertEqual(response.status_code, 200)
        mock_log_event.assert_called_once()
