"""
Custom allauth adapters for role-based user creation in CozyWish.

This module provides custom adapters for django-allauth that handle:
- Role-based user registration (customer vs service provider)
- Automatic profile creation based on user role
- Integration with existing business logic and security
- Social authentication with role assignment
- Phone number normalization and validation
- Email verification workflow maintenance
"""

import logging
from typing import Dict, Any, Optional

from django.conf import settings
from django.core.exceptions import ValidationError
from django.db import transaction
from django.http import HttpRequest
from django.utils.translation import gettext_lazy as _
from django.urls import reverse

from allauth.account.adapter import DefaultAccountAdapter
from allauth.account.models import EmailAddress
from allauth.socialaccount.adapter import DefaultSocialAccountAdapter
from allauth.socialaccount.models import SocialAccount
from allauth.exceptions import ImmediateHttpResponse

# Local imports
from .constants import UserRoles, UserStatus, DefaultValues
from .models import (
    CustomUser,
    UserPreferences, UserSecurity, EmailPreferences,
    ProfilePrivacySettings
)
from .models.customer import CustomerProfile
from .models.provider import ServiceProviderProfile
from .services import CustomerRegistrationService, ProviderRegistrationService
from .utils import normalize_phone
from .utils import log_account_lifecycle_event, log_authentication_event


logger = logging.getLogger(__name__)


class CozyWishAccountAdapter(DefaultAccountAdapter):
    """
    Custom account adapter for role-based user creation and management.
    
    This adapter extends DefaultAccountAdapter to provide:
    - Role-based user registration with automatic profile creation
    - Custom validation for role-specific signup requirements
    - Integration with existing business logic and security features
    - Enhanced email verification workflow
    - Phone number normalization and validation
    """
    
    def __init__(self, request=None):
        super().__init__(request)
        self.request = request
        
        # Ensure request has session for allauth compatibility
        if request and not hasattr(request, 'session'):
            logger.warning(f"Request missing session attribute, creating empty session: {request}")
            # Create a minimal session-like object to prevent errors
            class DummySession:
                def __init__(self):
                    self._data = {}
                def get(self, key, default=None):
                    return self._data.get(key, default)
                def set(self, key, value):
                    self._data[key] = value
            request.session = DummySession()
    
    def save_user(self, request: HttpRequest, user: CustomUser, form, commit: bool = True) -> CustomUser:
        """
        Save user with role-based profile creation and business logic integration.
        
        This method handles:
        - Role assignment based on signup context
        - Automatic profile creation (CustomerProfile or ServiceProviderProfile)
        - Security record creation (UserSecurity, UserPreferences, EmailPreferences)
        - Phone number normalization
        - Business logic integration with existing services
        
        Args:
            request: HTTP request object
            user: CustomUser instance to save
            form: The signup form with user data
            commit: Whether to save the user to database
            
        Returns:
            CustomUser: The saved user instance with complete profile setup
        """
        # Ensure request has session for allauth compatibility
        if request and not hasattr(request, 'session'):
            logger.warning(f"Request missing session in save_user, creating empty session: {request}")
            # Create a minimal session-like object to prevent errors
            class DummySession:
                def __init__(self):
                    self._data = {}
                def get(self, key, default=None):
                    return self._data.get(key, default)
                def set(self, key, value):
                    self._data[key] = value
            request.session = DummySession()
            
        try:
            with transaction.atomic():
                # Extract role from form or request context
                role = self._determine_user_role(request, form)
                
                # Set basic user fields
                user.role = role
                
                # Handle email verification based on role
                if role == UserRoles.CUSTOMER:
                    # Customers don't need email verification - activate immediately
                    user.status = UserStatus.ACTIVE
                    user.email_verified = True
                    user.is_active = True
                else:
                    # Service providers need email verification
                    user.status = UserStatus.PENDING_VERIFICATION
                    user.email_verified = False
                    user.is_active = True  # Activated but requires email verification

                # Populate credentials from the signup form so model validation passes
                if hasattr(form, 'cleaned_data'):
                    user.email = form.cleaned_data.get('email', '')
                    raw_password = form.cleaned_data.get('password1') or form.cleaned_data.get('password')
                    if raw_password:
                        user.set_password(raw_password)
                
                # Extract and normalize additional data from form
                additional_data = self._extract_form_data(form)
                
                # Validate role-specific requirements
                self._validate_role_requirements(role, additional_data)
                
                # Save the user first
                if commit:
                    user.save()
                    
                    # Create role-specific profile and related records
                    self._create_user_profile(user, role, additional_data, request)
                    self._create_security_records(user)
                    self._create_preference_records(user)
                    
                    # Log account creation
                    log_account_lifecycle_event(
                        event_type='registration',
                        user=user,
                        request=None,
                        reason='email_signup'
                    )
                    
                    logger.info(f"User account created successfully: {user.email} (role: {role})")
                
                return user
                
        except Exception as e:
            logger.error(f"Error saving user during registration: {str(e)}", exc_info=True)
            raise ValidationError(_("Registration failed. Please try again."))
    
    def add_email(self, request: HttpRequest, user: CustomUser, email: str) -> None:
        """
        Override add_email to immediately verify customer email addresses.
        
        Args:
            request: HTTP request object
            user: User object
            email: Email address being added
        """
        # Call parent method to create EmailAddress
        super().add_email(request, user, email)
        
        # For customers, immediately mark email as verified
        if user.role == UserRoles.CUSTOMER:
            try:
                from allauth.account.models import EmailAddress
                email_address = EmailAddress.objects.get(user=user, email=email)
                if not email_address.verified:
                    email_address.verified = True
                    email_address.primary = True
                    email_address.save()
                    logger.info(f"Customer EmailAddress immediately verified: {email}")
            except EmailAddress.DoesNotExist:
                logger.warning(f"EmailAddress not found for customer: {email}")
            except Exception as e:
                logger.error(f"Error verifying customer EmailAddress: {str(e)}")
    
    def _determine_user_role(self, request: HttpRequest, form) -> str:
        """
        Determine user role based on form data and request context.
        
        Priority:
        1. URL path analysis (e.g., /provider/signup/) - highest priority
        2. Form field 'role' if explicitly provided and different from default
        3. Request parameter 'role'
        4. Default to customer
        
        Args:
            request: HTTP request object
            form: The signup form
            
        Returns:
            str: User role (customer, service_provider, or admin)
        """
        # Analyze URL path for role context (highest priority for auto-determination)
        path = request.path.lower()
        if 'provider' in path or 'business' in path:
            return UserRoles.SERVICE_PROVIDER
        elif 'customer' in path:
            return UserRoles.CUSTOMER
        elif 'admin' in path:
            return UserRoles.ADMIN
        
        # Check form data if URL doesn't specify role and form has explicit non-default value
        if hasattr(form, 'cleaned_data') and 'role' in form.cleaned_data:
            role = form.cleaned_data['role']
            # Only use form role if it's explicitly set to something other than default
            if role and role != UserRoles.CUSTOMER and role in [UserRoles.SERVICE_PROVIDER, UserRoles.ADMIN]:
                return role
        
        # Check request parameters as fallback
        role = request.GET.get('role') or request.POST.get('role')
        if role in [UserRoles.CUSTOMER, UserRoles.SERVICE_PROVIDER, UserRoles.ADMIN]:
            return role
        
        # Default to customer
        return UserRoles.CUSTOMER
    
    def _extract_form_data(self, form) -> Dict[str, Any]:
        """
        Extract additional data from the signup form.
        
        Args:
            form: The signup form
            
        Returns:
            Dict containing extracted form data
        """
        data = {}
        
        if not hasattr(form, 'cleaned_data'):
            return data
        
        # Common fields
        fields_to_extract = [
            'first_name', 'last_name', 'phone_number', 'gender',
            'birth_month', 'birth_year', 'address', 'city', 'state',
            'zip_code', 'business_name', 'contact_person_name', 
            'ein', 'website', 'description'
        ]
        
        for field in fields_to_extract:
            if field in form.cleaned_data:
                data[field] = form.cleaned_data[field]
        
        # Normalize phone numbers
        for phone_field in ['phone_number']:
            if phone_field in data and data[phone_field]:
                try:
                    data[phone_field] = normalize_phone(data[phone_field])
                except ValidationError:
                    # Log but don't fail - let validation handle it later
                    logger.warning(f"Invalid phone number format: {data[phone_field]}")
        
        return data
    
    def _validate_role_requirements(self, role: str, data: Dict[str, Any]) -> None:
        """
        Validate role-specific requirements during signup.
        
        Args:
            role: User role
            data: Form data dictionary
            
        Raises:
            ValidationError: If role-specific requirements are not met
        """
        if role == UserRoles.SERVICE_PROVIDER:
            # Service providers require business information
            required_fields = ['business_name', 'contact_person_name']
            missing_fields = [field for field in required_fields if not data.get(field)]
            
            if missing_fields:
                raise ValidationError(
                    _("Service provider registration requires: {}").format(
                        ', '.join(missing_fields)
                    )
                )
        
        elif role == UserRoles.ADMIN:
            # Admin accounts require special authorization
            # This should typically be created through Django admin or management commands
            raise ValidationError(_("Admin accounts cannot be created through public registration."))
    
    def _create_user_profile(self, user: CustomUser, role: str, data: Dict[str, Any], request: HttpRequest) -> None:
        """
        Create role-specific user profile.
        
        Args:
            user: CustomUser instance
            role: User role
            data: Form data dictionary
            request: HTTP request object
        """
        if role == UserRoles.CUSTOMER:
            CustomerProfile.objects.create(
                user=user,
                first_name=data.get('first_name', ''),
                last_name=data.get('last_name', ''),
                phone_number=data.get('phone_number', ''),
                gender=data.get('gender', ''),
                birth_month=data.get('birth_month') or None,
                birth_year=data.get('birth_year') or None,
                address=data.get('address', ''),
                city=data.get('city', ''),
                zip_code=data.get('zip_code', '')
            )
            
        elif role == UserRoles.SERVICE_PROVIDER:
            ServiceProviderProfile.objects.create(
                user=user,
                legal_name=data.get('business_name', ''),
                display_name=data.get('business_name', ''),  # Can be customized later
                description=data.get('description', ''),
                phone='',  # Business phone will be added later on venue page
                contact_name=data.get('contact_person_name', ''),
                address='',  # Business address will be added later on venue page
                city='',  # City will be added later on venue page
                state='',  # State will be added later on venue page
                zip_code='',  # ZIP code will be added later on venue page
                ein=data.get('ein', ''),
                website=data.get('website', '')
            )
    
    def _create_security_records(self, user: CustomUser) -> None:
        """
        Create security-related records for the user.
        
        Args:
            user: CustomUser instance
        """
        # Create user security record
        UserSecurity.objects.create(user=user)
        
        # Note: Additional security setup like 2FA can be added here
    
    def _create_preference_records(self, user: CustomUser) -> None:
        """
        Create preference and privacy records for the user.
        
        Args:
            user: CustomUser instance
        """
        # Create user preferences with defaults
        UserPreferences.objects.create(user=user)
        
        # Create email preferences with role-appropriate defaults
        email_prefs = {
            'user': user,
            'email_notifications_enabled': True,
            'account_notifications': True,
            'booking_notifications': True,
            'payment_notifications': True,
            'marketing_emails': False,  # Opt-out by default
            'event_notifications': user.is_customer,  # Only for customers by default
            'review_notifications': True,
        }
        EmailPreferences.objects.create(**email_prefs)
        
        # Create privacy settings with secure defaults
        privacy_settings = ProfilePrivacySettings.get_default_settings()
        ProfilePrivacySettings.objects.create(user=user, **privacy_settings)
    
    def clean_email(self, email: str) -> str:
        """
        Clean and validate email address with enhanced security.
        
        Args:
            email: Email address to clean
            
        Returns:
            str: Cleaned email address
            
        Raises:
            ValidationError: If email is invalid or blocked
        """
        email = super().clean_email(email)
        
        # Additional email validation can be added here
        # For example, checking against blocked domains
        blocked_domains = ['tempmail.com', 'guerrillamail.com', '10minutemail.com']
        if '@' not in email:
            return email
        domain = email.split('@', 1)[1].lower()
        
        if domain in blocked_domains:
            raise ValidationError(
                _('Email addresses from this domain are not allowed.')
            )
        
        return email
    
    def is_open_for_signup(self, request: HttpRequest) -> bool:
        """
        Determine if signup is currently open.
        
        Args:
            request: HTTP request object
            
        Returns:
            bool: True if signup is open
        """
        # Check if registration is globally disabled
        if hasattr(settings, 'ACCOUNT_ALLOW_REGISTRATION'):
            if not settings.ACCOUNT_ALLOW_REGISTRATION:
                return False
        
        # Role-specific signup restrictions
        role = self._determine_user_role(request, None)
        
        # You can add role-specific restrictions here
        # For example, temporarily disable service provider signups
        if role == UserRoles.SERVICE_PROVIDER:
            if hasattr(settings, 'DISABLE_PROVIDER_SIGNUP'):
                if settings.DISABLE_PROVIDER_SIGNUP:
                    return False
        
        return super().is_open_for_signup(request)
    
    def is_email_verification_required(self, request: HttpRequest, user: CustomUser = None) -> bool:
        """
        Determine if email verification is required based on user role.
        
        Args:
            request: HTTP request object
            user: User object (if available)
            
        Returns:
            bool: True if email verification is required
        """
        # If we have a user object, check their role
        if user:
            # Service providers always need email verification
            if user.role == UserRoles.SERVICE_PROVIDER:
                return True
            # Customers don't need email verification
            elif user.role == UserRoles.CUSTOMER:
                return False
        
        # If no user object, determine role from request context
        role = self._determine_user_role(request, None)
        
        # Service providers need email verification, customers don't
        if role == UserRoles.SERVICE_PROVIDER:
            return True
        elif role == UserRoles.CUSTOMER:
            return False
        
        # Default to requiring verification for other roles
        return True
    
    def send_confirmation_mail(self, request: HttpRequest, emailconfirmation, signup: bool) -> None:
        """
        Send email confirmation with enhanced context and security.
        
        Args:
            request: HTTP request object
            emailconfirmation: EmailConfirmation instance
            signup: Whether this is a signup confirmation
        """
        try:
            # Add custom context for email templates
            user = emailconfirmation.email_address.user
            
            # Check if email verification is required for this user
            if not self.is_email_verification_required(request, user):
                # Skip email verification for customers
                logger.info(f"Skipping email verification for customer: {user.email}")
                return
            
            # Log email confirmation send
            log_authentication_event(
                event_type='email_confirmation_sent',
                user_email=user.email,
                success=True,
                request=request,
                additional_data={
                    'is_signup': signup,
                    'email': emailconfirmation.email_address.email
                }
            )
            
            super().send_confirmation_mail(request, emailconfirmation, signup)
            
        except Exception as e:
            logger.error(f"Error sending confirmation email: {str(e)}", exc_info=True)
            raise
    
    def confirm_email(self, request: HttpRequest, email_address: EmailAddress) -> None:
        """
        Confirm email address with business logic integration.
        
        Args:
            request: HTTP request object
            email_address: EmailAddress instance to confirm
        """
        user = email_address.user
        
        try:
            with transaction.atomic():
                # Call parent method
                super().confirm_email(request, email_address)
                
                # Update user verification status
                user.email_verified = True
                user.status = UserStatus.ACTIVE
                user.save(update_fields=['email_verified', 'status'])
                
                # Log email confirmation
                log_account_lifecycle_event(
                    event_type='email_verification',
                    user=user,
                    request=request,
                    details={'email': email_address.email}
                )
                
                logger.info(f"Email confirmed successfully: {user.email}")
                
        except Exception as e:
            logger.error(f"Error confirming email: {str(e)}", exc_info=True)
            raise

    def is_login_by_code_required(self, login):
        """Return whether a login-by-code challenge is required.

        The default implementation in ``DefaultAccountAdapter`` relies on
        ``self.request`` always being present and containing a valid session.
        In some edge-cases (for example when the adapter instance was created
        without an attached request object) this attribute can be ``None``
        which causes an ``AttributeError`` inside django-allauth.

        We defensively short-circuit in that situation and fall back to the
        default behaviour **without** forcing a code-based login step. This
        keeps the signup / login flow functional while still delegating to the
        parent implementation whenever a request is available.
        """
        # If the adapter instance has no request assigned we cannot inspect the
        # session – simply assume that no additional login-by-code step is
        # required.
        if self.request is None:
            logger.warning(
                "CozyWishAccountAdapter.is_login_by_code_required called without a request; "
                "skipping login-by-code requirement check to avoid AttributeError."
            )
            return False

        # Ensure the request object has a usable session (added as an extra
        # safeguard – some custom middlewares/tests may strip it off).
        if not hasattr(self.request, "session"):
            class DummySession(dict):
                def get(self, key, default=None):
                    return super().get(key, default)
            self.request.session = DummySession()

        # Delegate to the upstream implementation now that we are certain the
        # prerequisites are in place.
        try:
            return super().is_login_by_code_required(login)
        except Exception as exc:  # pragma: no cover – defensive catch-all
            # Log the problem, but never break the authentication flow.
            logger.error(
                "Error while determining login-by-code requirement: %s. Falling back to 'False'.",
                exc,
                exc_info=True,
            )
            return False

    def get_logout_redirect_url(self, request: HttpRequest) -> str:
        """
        Get the logout redirect URL based on user role.
        
        Args:
            request: HTTP request object
            
        Returns:
            str: Redirect URL after logout
        """
        try:
            # Check if user is authenticated and has a role
            if request.user.is_authenticated:
                if request.user.role == UserRoles.CUSTOMER:
                    return reverse('home_app:home')  # Customers go to home page
                elif request.user.role == UserRoles.SERVICE_PROVIDER:
                    return reverse('accounts_app:service_provider_login')  # Providers go to provider login
                else:
                    return reverse('home_app:home')  # Default to home page
            
            # For anonymous users, go to home page
            return reverse('home_app:home')
            
        except Exception as e:
            logger.error(f"Error determining logout redirect URL: {str(e)}")
            return reverse('home_app:home')  # Fallback to home page

    def is_open_for_login(self, request: HttpRequest, email: str) -> bool:
        """
        Override login validation to allow customers to login without email verification.
        
        Args:
            request: HTTP request object
            email: Email address attempting to login
            
        Returns:
            bool: True if login is allowed
        """
        try:
            # Try to get the user
            user = CustomUser.objects.get(email=email)
            
            # For customers, allow login regardless of email verification status
            if user.role == UserRoles.CUSTOMER:
                logger.info(f"Allowing customer login without email verification: {email}")
                return True
            
            # For service providers, require email verification
            if user.role == UserRoles.SERVICE_PROVIDER:
                if not user.email_verified:
                    logger.warning(f"Service provider login blocked - email not verified: {email}")
                    return False
                return True
            
            # For other roles, use default behavior
            return super().is_open_for_login(request, email)
            
        except CustomUser.DoesNotExist:
            # User doesn't exist, let allauth handle the error
            return super().is_open_for_login(request, email)
        except Exception as e:
            logger.error(f"Error checking login access for {email}: {str(e)}")
            return super().is_open_for_login(request, email)
    
    def pre_login(self, request: HttpRequest, user: CustomUser, **kwargs) -> None:
        """
        Ensure customers can login even if EmailAddress is not verified.
        
        Args:
            request: HTTP request object
            user: User attempting to login
            **kwargs: Additional arguments
        """
        try:
            # For customers, ensure EmailAddress is marked as verified
            if user.role == UserRoles.CUSTOMER:
                from allauth.account.models import EmailAddress
                email_address, created = EmailAddress.objects.get_or_create(
                    user=user,
                    email=user.email,
                    defaults={
                        'verified': True,
                        'primary': True
                    }
                )
                if not email_address.verified:
                    email_address.verified = True
                    email_address.primary = True
                    email_address.save()
                    logger.info(f"Customer EmailAddress verified during login: {user.email}")
                
                # Also ensure user model has email_verified = True
                if not user.email_verified:
                    user.email_verified = True
                    user.save(update_fields=['email_verified'])
                    logger.info(f"Customer user.email_verified set to True: {user.email}")
            
            # Call parent method
            super().pre_login(request, user, **kwargs)
            
        except Exception as e:
            logger.error(f"Error in pre_login for {user.email}: {str(e)}")
            # Don't let this break the login process
            super().pre_login(request, user, **kwargs)
    
    def new_user(self, request: HttpRequest) -> CustomUser:
        """
        Override new user creation to handle role-based email verification setup.
        
        Args:
            request: HTTP request object
            
        Returns:
            CustomUser: New user instance
        """
        user = super().new_user(request)
        
        # Determine role and set it early
        role = self._determine_user_role(request, None)
        user.role = role
        
        # For customers, set email_verified to True from the start
        if role == UserRoles.CUSTOMER:
            user.email_verified = True
            user.status = UserStatus.ACTIVE
            logger.info(f"New customer user - email verification disabled: {user.email}")
        
        return user
    



class CozyWishSocialAccountAdapter(DefaultSocialAccountAdapter):
    """
    Custom social account adapter for role-based social authentication.
    
    This adapter extends DefaultSocialAccountAdapter to provide:
    - Role assignment for social signups
    - Profile creation from social account data
    - Integration with existing business logic
    - Enhanced social account validation
    """
    
    def __init__(self, request=None):
        super().__init__(request)
        self.request = request
    
    def pre_social_login(self, request: HttpRequest, sociallogin) -> None:
        """
        Perform actions before social login/signup.
        
        Args:
            request: HTTP request object
            sociallogin: SocialLogin instance
        """
        # Check if social login is allowed
        if not self._is_social_login_allowed(request, sociallogin):
            raise ImmediateHttpResponse(
                response=self._render_social_login_error(
                    request, "Social login is currently disabled."
                )
            )
        
        super().pre_social_login(request, sociallogin)
    
    def save_user(self, request: HttpRequest, sociallogin, form=None) -> CustomUser:
        """
        Save user from social account with role-based profile creation.
        
        Args:
            request: HTTP request object
            sociallogin: SocialLogin instance
            form: Additional form data if provided
            
        Returns:
            CustomUser: The created/updated user instance
        """
        try:
            with transaction.atomic():
                # Create user using parent method
                user = super().save_user(request, sociallogin, form)
                
                # Set role and status
                role = self._determine_social_user_role(request, sociallogin, form)
                user.role = role
                user.status = UserStatus.ACTIVE  # Social accounts are pre-verified
                user.email_verified = True  # Trust social provider email verification
                user.save()
                
                # Extract social account data
                social_data = self._extract_social_data(sociallogin)
                
                # Create profiles and records if this is a new user
                if not hasattr(user, 'customer_profile') and not hasattr(user, 'service_provider_profile'):
                    self._create_social_user_profile(user, role, social_data, request)
                    self._create_security_records(user)
                    self._create_preference_records(user)
                
                # Log social account creation/login
                log_account_lifecycle_event(
                    event_type='social_registration' if sociallogin.is_existing else 'social_login',
                    user=user,
                    request=request,
                    details={
                        'provider': sociallogin.account.provider,
                        'role': role,
                        'provider_uid': sociallogin.account.uid
                    }
                )
                
                logger.info(
                    f"Social user {'created' if not sociallogin.is_existing else 'logged in'}: "
                    f"{user.email} via {sociallogin.account.provider}"
                )
                
                return user
                
        except Exception as e:
            logger.error(f"Error saving social user: {str(e)}", exc_info=True)
            raise ValidationError(_("Social registration failed. Please try again."))
    
    def populate_user(self, request: HttpRequest, sociallogin, data: Dict[str, Any]) -> CustomUser:
        """
        Populate user instance with data from social provider.
        
        Args:
            request: HTTP request object
            sociallogin: SocialLogin instance
            data: Data from social provider
            
        Returns:
            CustomUser: Populated user instance
        """
        user = super().populate_user(request, sociallogin, data)
        
        # Extract additional data from social account
        extra_data = sociallogin.account.extra_data
        
        # Set additional fields if available
        if 'first_name' in extra_data:
            user.first_name = extra_data['first_name']
        if 'last_name' in extra_data:
            user.last_name = extra_data['last_name']
        
        # Set role (will be refined in save_user)
        user.role = self._determine_social_user_role(request, sociallogin, None)
        
        return user
    
    def _determine_social_user_role(self, request: HttpRequest, sociallogin, form) -> str:
        """
        Determine user role for social account signup.
        
        Args:
            request: HTTP request object
            sociallogin: SocialLogin instance
            form: Additional form data if provided
            
        Returns:
            str: User role
        """
        # Check form data first
        if form and hasattr(form, 'cleaned_data') and 'role' in form.cleaned_data:
            role = form.cleaned_data['role']
            if role in [UserRoles.CUSTOMER, UserRoles.SERVICE_PROVIDER]:
                return role
        
        # Check request parameters
        role = request.GET.get('role') or request.POST.get('role')
        if role in [UserRoles.CUSTOMER, UserRoles.SERVICE_PROVIDER]:
            return role
        
        # Analyze URL path
        path = request.path.lower()
        if 'provider' in path or 'business' in path:
            return UserRoles.SERVICE_PROVIDER
        
        # Default to customer for social signups
        return UserRoles.CUSTOMER
    
    def _extract_social_data(self, sociallogin) -> Dict[str, Any]:
        """
        Extract user data from social account.
        
        Args:
            sociallogin: SocialLogin instance
            
        Returns:
            Dict containing extracted social data
        """
        extra_data = sociallogin.account.extra_data
        data = {}
        
        # Common mappings for different providers
        field_mappings = {
            'google': {
                'first_name': 'given_name',
                'last_name': 'family_name',
                'profile_picture': 'picture',
                'phone_number': 'phone_number'
            },
            'facebook': {
                'first_name': 'first_name',
                'last_name': 'last_name',
                'profile_picture': 'picture.data.url',
                'phone_number': 'phone'
            },
            'linkedin': {
                'first_name': 'firstName',
                'last_name': 'lastName',
                'profile_picture': 'pictureUrl'
            }
        }
        
        provider = sociallogin.account.provider
        mappings = field_mappings.get(provider, {})
        
        for internal_field, external_field in mappings.items():
            # Handle nested fields (e.g., picture.data.url)
            if '.' in external_field:
                value = extra_data
                for key in external_field.split('.'):
                    value = value.get(key, {}) if isinstance(value, dict) else None
                    if value is None:
                        break
            else:
                value = extra_data.get(external_field)
            
            if value:
                data[internal_field] = value
        
        # Normalize phone number if present
        if 'phone_number' in data and data['phone_number']:
            try:
                data['phone_number'] = normalize_phone(data['phone_number'])
            except ValidationError:
                # Remove invalid phone number
                del data['phone_number']
        
        return data
    
    def _create_social_user_profile(self, user: CustomUser, role: str, data: Dict[str, Any], request: HttpRequest) -> None:
        """
        Create user profile from social account data.
        
        Args:
            user: CustomUser instance
            role: User role
            data: Social account data
            request: HTTP request object
        """
        if role == UserRoles.CUSTOMER:
            CustomerProfile.objects.create(
                user=user,
                first_name=data.get('first_name', user.first_name),
                last_name=data.get('last_name', user.last_name),
                phone_number=data.get('phone_number', ''),
                # Note: profile_picture handling would need additional logic for downloading/storing
            )
            
        elif role == UserRoles.SERVICE_PROVIDER:
            # For service providers via social, create minimal profile
            # They'll need to complete business information later
            ServiceProviderProfile.objects.create(
                user=user,
                legal_name=f"{user.first_name} {user.last_name}".strip() or user.email,
                contact_name=f"{user.first_name} {user.last_name}".strip() or user.email,
                phone=data.get('phone_number', ''),
                # Other fields will be empty and need completion
                address='',
                city='',
                state='',
                zip_code=''
            )
    
    def _create_security_records(self, user: CustomUser) -> None:
        """Create security records for social user."""
        UserSecurity.objects.create(user=user)
    
    def _create_preference_records(self, user: CustomUser) -> None:
        """Create preference records for social user."""
        # Create user preferences
        UserPreferences.objects.create(user=user)
        
        # Create email preferences
        EmailPreferences.objects.create(
            user=user,
            email_notifications_enabled=True,
            account_notifications=True,
            booking_notifications=True,
            payment_notifications=True,
            marketing_emails=False,  # Conservative default for social users
            event_notifications=user.is_customer,
            review_notifications=True
        )
        
        # Create privacy settings
        privacy_settings = ProfilePrivacySettings.get_default_settings()
        ProfilePrivacySettings.objects.create(user=user, **privacy_settings)
    
    def _is_social_login_allowed(self, request: HttpRequest, sociallogin) -> bool:
        """
        Check if social login is allowed.
        
        Args:
            request: HTTP request object
            sociallogin: SocialLogin instance
            
        Returns:
            bool: True if social login is allowed
        """
        # Check global settings
        if hasattr(settings, 'SOCIALACCOUNT_ALLOW_REGISTRATION'):
            if not settings.SOCIALACCOUNT_ALLOW_REGISTRATION:
                return False
        
        # Provider-specific restrictions
        provider = sociallogin.account.provider
        disabled_providers = getattr(settings, 'DISABLED_SOCIAL_PROVIDERS', [])
        if provider in disabled_providers:
            return False
        
        return True
    
    def _render_social_login_error(self, request: HttpRequest, message: str):
        """
        Render social login error page.
        
        Args:
            request: HTTP request object
            message: Error message
            
        Returns:
            HttpResponse: Error page response
        """
        from django.shortcuts import render
        from django.http import HttpResponse
        
        context = {
            'error_message': message,
            'support_email': getattr(settings, 'SUPPORT_EMAIL', '<EMAIL>')
        }
        
        return render(request, 'account/social_login_error.html', context)
    
    def is_open_for_signup(self, request: HttpRequest, sociallogin) -> bool:
        """
        Check if social signup is open.
        
        Args:
            request: HTTP request object
            sociallogin: SocialLogin instance
            
        Returns:
            bool: True if social signup is open
        """
        if not super().is_open_for_signup(request, sociallogin):
            return False
        
        return self._is_social_login_allowed(request, sociallogin) 
    
    def _render_social_login_error(self, request: HttpRequest, message: str):
        """
        Render social login error page.
        
        Args:
            request: HTTP request object
            message: Error message
            
        Returns:
            HttpResponse: Error page response
        """
        from django.shortcuts import render
        from django.http import HttpResponse
        
        context = {
            'error_message': message,
            'support_email': getattr(settings, 'SUPPORT_EMAIL', '<EMAIL>')
        }
        
        return render(request, 'account/social_login_error.html', context)
    
    def is_open_for_signup(self, request: HttpRequest, sociallogin) -> bool:
        """
        Check if social signup is open.
        
        Args:
            request: HTTP request object
            sociallogin: SocialLogin instance
            
        Returns:
            bool: True if social signup is open
        """
        if not super().is_open_for_signup(request, sociallogin):
            return False
        
        return self._is_social_login_allowed(request, sociallogin) 