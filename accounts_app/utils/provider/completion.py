# --- Standard Library Imports ---
from typing import Dict, Any
from django.utils.translation import gettext_lazy as _


def calculate_provider_profile_completion(user) -> Dict[str, Any]:
    """
    Calculates profile completion percentage for service provider users.
    
    Args:
        user: CustomUser instance
        
    Returns:
        Dict containing completion percentage and missing fields
    """
    if not user.is_service_provider:
        return {'percentage': 0, 'missing_fields': [], 'completed_fields': []}
    
    # Get related profile
    try:
        profile = user.service_provider_profile
        preferences = user.user_preferences
    except:
        return {'percentage': 0, 'missing_fields': ['profile_not_created'], 'completed_fields': []}
    
    # Define required fields and their weights
    required_fields = {
        'legal_name': {'weight': 20, 'value': profile.legal_name},
        'display_name': {'weight': 10, 'value': profile.display_name},
        'description': {'weight': 10, 'value': profile.description},
        'logo': {'weight': 10, 'value': profile.logo},
        'phone': {'weight': 10, 'value': profile.phone},
        'contact_name': {'weight': 10, 'value': profile.contact_name},
        'address': {'weight': 10, 'value': profile.address},
        'city': {'weight': 5, 'value': profile.city},
        'state': {'weight': 5, 'value': profile.state},
        'zip_code': {'weight': 5, 'value': profile.zip_code},
        'email_verified': {'weight': 5, 'value': user.email_verified},
    }
    
    completed_fields = []
    missing_fields = []
    total_score = 0
    
    for field_name, field_info in required_fields.items():
        if field_info['value']:
            completed_fields.append(field_name)
            total_score += field_info['weight']
        else:
            missing_fields.append(field_name)
    
    percentage = min(100, total_score)
    
    return {
        'percentage': percentage,
        'missing_fields': missing_fields,
        'completed_fields': completed_fields,
        'total_fields': len(required_fields)
    } 