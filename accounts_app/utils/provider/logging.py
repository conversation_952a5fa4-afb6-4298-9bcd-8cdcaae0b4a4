# --- Standard Library Imports ---
from typing import Any, Optional, Dict
from django.http import HttpRequest


def log_team_management_event(
    action: str,
    service_provider: Any,
    team_member_name: str,
    request: Optional[HttpRequest] = None,
    team_member_id: Optional[int] = None,
    additional_details: Optional[Dict[str, Any]] = None
) -> None:
    """
    Log additions, edits, and removals of team members.
    """
    # Import here to avoid circular imports
    from ..common.logging import log_user_activity
    
    details = {
        'action': action,
        'team_member_name': team_member_name,
        'team_member_id': team_member_id
    }
    if additional_details:
        details.update(additional_details)

    log_user_activity(
        activity_type=f'team_member_{action}',
        user=service_provider,
        request=request,
        details=details,
        target_object=f"team_member_{team_member_id}"
    ) 