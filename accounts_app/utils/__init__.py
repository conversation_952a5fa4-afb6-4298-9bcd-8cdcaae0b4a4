"""
Utils Module for Accounts App

This module provides convenient imports for common utility functions
used throughout the accounts_app.
"""

# --- Common/Shared Functions ---
# Image processing and validation
from .common.image.processing import (
    process_profile_image,
    create_thumbnail,
)
from .common.image.validation import (
    validate_image_file,
)

# Shared profile completion
from .common.profile.completion import (
    get_profile_completion_suggestions,
)

# Shared file path generators
from .common.profile.paths import (
    get_profile_thumbnail_path,
)

# --- Customer-specific Functions ---
from .customer.completion import (
    calculate_customer_profile_completion,
)
from .customer.paths import (
    get_customer_profile_image_path,
)

# --- Provider-specific Functions ---
from .provider.completion import (
    calculate_provider_profile_completion,
)
from .provider.paths import (
    get_provider_profile_image_path,
    get_staff_profile_image_path,
)
from .provider.logging import (
    log_team_management_event,
)

# --- Logging Functions ---
from .common.logging import (
    # Client info
    get_client_info,
    
    # Security logging
    log_security_event,
    log_authentication_event,
    
    # Activity logging
    log_user_activity,
    log_profile_change,
    log_account_lifecycle_event,
    log_data_access_event,
    
    # Error logging
    log_error,
    log_info,
    
    # Performance logging
    log_performance,
    performance_monitor,
    
    # Audit logging
    log_audit_event,
)

# --- Validators ---
from .common.validators import (
    # Phone validation
    normalize_phone,
    
    # Password validators
    ComplexityPasswordValidator,
    PasswordHistoryValidator,
    CustomMinimumLengthValidator,
    NoPersonalInformationValidator,
    NoSequentialCharactersValidator,
    NoRepeatedCharactersValidator,
)

# --- Convenience module imports ---
# Import entire modules for direct access
from . import common
from . import customer
from . import provider

# --- Convenience exports ---
__all__ = [
    # Common helper functions
    'validate_image_file',
    'process_profile_image',
    'create_thumbnail',
    'get_profile_completion_suggestions',
    'get_profile_thumbnail_path',
    
    # Customer-specific functions
    'calculate_customer_profile_completion',
    'get_customer_profile_image_path',
    
    # Provider-specific functions
    'calculate_provider_profile_completion',
    'get_provider_profile_image_path',
    'get_staff_profile_image_path',
    'log_team_management_event',
    
    # Logging functions
    'get_client_info',
    'log_security_event',
    'log_authentication_event',
    'log_user_activity',
    'log_profile_change',
    'log_account_lifecycle_event',
    'log_data_access_event',
    'log_error',
    'log_info',
    'log_performance',
    'performance_monitor',
    'log_audit_event',
    
    # Validators
    'normalize_phone',
    'ComplexityPasswordValidator',
    'PasswordHistoryValidator',
    'CustomMinimumLengthValidator',
    'NoPersonalInformationValidator',
    'NoSequentialCharactersValidator',
    'NoRepeatedCharactersValidator',
] 