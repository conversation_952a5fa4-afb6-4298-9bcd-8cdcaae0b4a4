# --- Standard Library Imports ---
from typing import Dict, Any
from django.utils.translation import gettext_lazy as _


def calculate_customer_profile_completion(user) -> Dict[str, Any]:
    """
    Calculates profile completion percentage for customer users.
    
    Args:
        user: CustomUser instance
        
    Returns:
        Dict containing completion percentage and missing fields
    """
    if not user.is_customer:
        return {'percentage': 0, 'missing_fields': [], 'completed_fields': []}
    
    # Get related profiles
    try:
        profile = user.customer_profile
        user_profile = user.user_profile
        preferences = user.user_preferences
    except:
        return {'percentage': 0, 'missing_fields': ['profile_not_created'], 'completed_fields': []}
    
    # Define required fields and their weights
    required_fields = {
        'first_name': {'weight': 15, 'value': profile.first_name},
        'last_name': {'weight': 15, 'value': profile.last_name},
        'profile_picture': {'weight': 10, 'value': profile.profile_picture},
        'phone_number': {'weight': 10, 'value': profile.phone_number},
        'gender': {'weight': 5, 'value': profile.gender},
        'birth_month': {'weight': 5, 'value': profile.birth_month},
        'birth_year': {'weight': 5, 'value': profile.birth_year},
        'address': {'weight': 10, 'value': profile.address},
        'city': {'weight': 10, 'value': profile.city},
        'zip_code': {'weight': 10, 'value': profile.zip_code},
        'email_verified': {'weight': 5, 'value': user.email_verified},
    }
    
    completed_fields = []
    missing_fields = []
    total_score = 0
    
    for field_name, field_info in required_fields.items():
        if field_info['value']:
            completed_fields.append(field_name)
            total_score += field_info['weight']
        else:
            missing_fields.append(field_name)
    
    percentage = min(100, total_score)
    
    return {
        'percentage': percentage,
        'missing_fields': missing_fields,
        'completed_fields': completed_fields,
        'total_fields': len(required_fields)
    } 