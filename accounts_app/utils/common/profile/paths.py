# --- Standard Library Imports ---
import os
import uuid
from typing import Optional


def _generate_filename(prefix: str, identifier: Optional[str], filename: str) -> str:
    """
    Generates a unique filename with prefix and identifier.
    
    Args:
        prefix: Prefix for the filename
        identifier: Optional identifier (usually user ID)
        filename: Original filename
        
    Returns:
        str: Generated unique filename
    """
    ext = filename.split('.')[-1]
    unique_id = uuid.uuid4().hex
    identifier_part = f"{identifier}_" if identifier else ""
    return f"{prefix}_{identifier_part}{unique_id}.{ext}"


def get_profile_thumbnail_path(instance, filename: str) -> str:
    """
    Generates file path for profile picture thumbnails.
    
    Args:
        instance: Model instance
        filename: Original filename
        
    Returns:
        str: File path for profile thumbnail
    """
    return os.path.join('thumbnails', 'profile_images', _generate_filename('thumb', str(instance.user.id), filename)) 