# --- Standard Library Imports ---
from typing import Dict, Any
from django.utils.translation import gettext_lazy as _


def get_profile_completion_suggestions(user) -> Dict[str, Any]:
    """
    Returns suggestions for completing user profile.
    
    Args:
        user: CustomUser instance
        
    Returns:
        Dict containing suggestions and priority actions
    """
    # Import here to avoid circular imports
    from ...customer.completion import calculate_customer_profile_completion
    from ...provider.completion import calculate_provider_profile_completion
    
    if user.is_customer:
        completion_data = calculate_customer_profile_completion(user)
    elif user.is_service_provider:
        completion_data = calculate_provider_profile_completion(user)
    else:
        return {'suggestions': [], 'priority_actions': []}
    
    # Define suggestions based on missing fields
    field_suggestions = {
        'first_name': {
            'message': _('Add your first name to personalize your profile'),
            'priority': 'high',
            'action': 'complete_basic_info'
        },
        'last_name': {
            'message': _('Add your last name to complete your identity'),
            'priority': 'high',
            'action': 'complete_basic_info'
        },
        'profile_picture': {
            'message': _('Upload a profile picture to make your profile more personal'),
            'priority': 'medium',
            'action': 'upload_photo'
        },
        'phone_number': {
            'message': _('Add your phone number for better communication'),
            'priority': 'medium',
            'action': 'add_contact_info'
        },
        'address': {
            'message': _('Add your address for location-based services'),
            'priority': 'medium',
            'action': 'add_location'
        },
        'email_verified': {
            'message': _('Verify your email address to secure your account'),
            'priority': 'critical',
            'action': 'verify_email'
        },
        'legal_name': {
            'message': _('Add your business legal name (required for providers)'),
            'priority': 'critical',
            'action': 'complete_business_info'
        },
        'logo': {
            'message': _('Upload your business logo to build brand recognition'),
            'priority': 'medium',
            'action': 'upload_logo'
        },
        'description': {
            'message': _('Add a business description to attract customers'),
            'priority': 'high',
            'action': 'complete_business_info'
        },
    }
    
    suggestions = []
    priority_actions = []
    
    for field in completion_data['missing_fields']:
        if field in field_suggestions:
            suggestion = field_suggestions[field]
            suggestions.append(suggestion)
            
            if suggestion['priority'] == 'critical':
                priority_actions.append(suggestion)
    
    # Sort suggestions by priority
    priority_order = {'critical': 0, 'high': 1, 'medium': 2, 'low': 3}
    suggestions.sort(key=lambda x: priority_order.get(x['priority'], 3))
    
    return {
        'suggestions': suggestions,
        'priority_actions': priority_actions,
        'completion_percentage': completion_data['percentage'],
        'missing_fields_count': len(completion_data['missing_fields'])
    } 