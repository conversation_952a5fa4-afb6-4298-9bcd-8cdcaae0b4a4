# --- Standard Library Imports ---
import re

# --- Third-Party Imports ---
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from django.contrib.auth.hashers import check_password
from django.contrib.auth import get_user_model


def normalize_phone(phone: str):
    """Validate and normalize a phone number."""
    if not phone:
        return phone
    
    # Remove all non-digit characters for validation
    digits_only = re.sub(r'\D', '', phone)
    
    if len(digits_only) < 10:
        raise ValidationError(_('Phone number must contain at least 10 digits.'))
    
    if len(digits_only) > 15:
        raise ValidationError(_('Phone number cannot exceed 15 digits (including country code).'))
    
    # Handle US phone numbers (10 or 11 digits)
    if len(digits_only) == 10:
        # Format as (XXX) XXX-XXXX
        area_code = digits_only[:3]
        exchange = digits_only[3:6]
        number = digits_only[6:10]
        return f"({area_code}) {exchange}-{number}"
    elif len(digits_only) == 11 and digits_only.startswith('1'):
        # Remove country code and format as (XXX) XXX-XXXX
        area_code = digits_only[1:4]
        exchange = digits_only[4:7]
        number = digits_only[7:11]
        return f"({area_code}) {exchange}-{number}"
    
    # Handle international numbers
    if phone.startswith('+'):
        return phone  # Keep international format as-is
    else:
        return f"+{digits_only}"  # Add + prefix for international numbers


class ComplexityPasswordValidator:
    """
    Validates password complexity requirements:
    - At least one uppercase letter
    - At least one lowercase letter
    - At least one digit
    - At least one special character
    """
    
    def __init__(self, min_special_chars=1, min_uppercase=1, min_lowercase=1, min_digits=1):
        self.min_special_chars = min_special_chars
        self.min_uppercase = min_uppercase
        self.min_lowercase = min_lowercase
        self.min_digits = min_digits
        
    def validate(self, password, user=None):
        uppercase_count = len(re.findall(r'[A-Z]', password))
        lowercase_count = len(re.findall(r'[a-z]', password))
        digit_count = len(re.findall(r'\d', password))
        special_count = len(re.findall(r'[!@#$%^&*(),.?":{}|<>]', password))
        
        errors = []
        
        if uppercase_count < self.min_uppercase:
            errors.append(_('Password must contain at least %(min)d uppercase letter(s).') % {'min': self.min_uppercase})
            
        if lowercase_count < self.min_lowercase:
            errors.append(_('Password must contain at least %(min)d lowercase letter(s).') % {'min': self.min_lowercase})
            
        if digit_count < self.min_digits:
            errors.append(_('Password must contain at least %(min)d digit(s).') % {'min': self.min_digits})
            
        if special_count < self.min_special_chars:
            # Escape the literal "%" in the error string by using "%%" so Python interpolation works correctly.
            errors.append(_('Password must contain at least %(min)d special character(s) (!@#$^%%&*(),.?":{}|<>).') % {
                'min': self.min_special_chars
            })
            
        if errors:
            raise ValidationError(errors)
    
    def get_help_text(self):
        return _(
            'Your password must contain at least %(uppercase)d uppercase letter, '
            '%(lowercase)d lowercase letter, %(digits)d digit, and %(special)d special character.'
        ) % {
            'uppercase': self.min_uppercase,
            'lowercase': self.min_lowercase,
            'digits': self.min_digits,
            'special': self.min_special_chars
        }


class PasswordHistoryValidator:
    """
    Validates that the new password is not one of the last N passwords used.
    """
    
    def __init__(self, history_count=5):
        self.history_count = history_count
        
    def validate(self, password, user=None):
        if user is None or not getattr(user, 'pk', None):
            # No user context or the user hasn't been persisted yet – skip history check.
            return
            
        # Import here to avoid circular imports
        from ..models import PasswordHistory
        
        # Get the last N passwords for this user
        recent_passwords = PasswordHistory.objects.filter(
            user=user
        ).order_by('-created_at')[:self.history_count]
        
        # Check if the new password matches any of the recent passwords
        for pwd_history in recent_passwords:
            if check_password(password, pwd_history.password_hash):
                raise ValidationError(
                    _('You cannot reuse any of your last %(count)d passwords.') % {'count': self.history_count}
                )
    
    def get_help_text(self):
        return _(
            'Your password cannot be the same as any of your last %(count)d passwords.'
        ) % {'count': self.history_count}


class CustomMinimumLengthValidator:
    """
    Custom minimum length validator with configurable length.
    """
    
    def __init__(self, min_length=12):
        self.min_length = min_length
        
    def validate(self, password, user=None):
        if len(password) < self.min_length:
            raise ValidationError(
                _('This password is too short. It must contain at least %(min_length)d characters.') % {'min_length': self.min_length}
            )
    
    def get_help_text(self):
        return _(
            'Your password must contain at least %(min_length)d characters.'
        ) % {'min_length': self.min_length}


class NoPersonalInformationValidator:
    """
    Validates that the password doesn't contain personal information.
    """
    
    def validate(self, password, user=None):
        if user is None:
            return
            
        # Convert password to lowercase for case-insensitive comparison
        password_lower = password.lower()
        
        # Check against various user attributes
        personal_info = []
        
        if hasattr(user, 'email') and user.email:
            personal_info.append(user.email.split('@')[0].lower())
            
        if hasattr(user, 'first_name') and user.first_name:
            personal_info.append(user.first_name.lower())
            
        if hasattr(user, 'last_name') and user.last_name:
            personal_info.append(user.last_name.lower())
            
        # Check customer profile
        if hasattr(user, 'customer_profile') and user.customer_profile:
            profile = user.customer_profile
            if profile.first_name:
                personal_info.append(profile.first_name.lower())
            if profile.last_name:
                personal_info.append(profile.last_name.lower())
                
        # Check service provider profile
        if hasattr(user, 'service_provider_profile') and user.service_provider_profile:
            profile = user.service_provider_profile
            if profile.legal_name:
                personal_info.append(profile.legal_name.lower())
            if profile.display_name:
                personal_info.append(profile.display_name.lower())
                
        # Check if password contains any personal information
        for info in personal_info:
            if info and len(info) >= 3 and info in password_lower:
                raise ValidationError(
                    _('Password cannot contain your personal information.')
                )
    
    def get_help_text(self):
        return _('Your password cannot contain your personal information such as your name or email.')


class NoSequentialCharactersValidator:
    """
    Validates that the password doesn't contain sequential characters.
    """
    
    def __init__(self, max_sequential=3):
        self.max_sequential = max_sequential
        
    def validate(self, password, user=None):
        # Check for sequential characters (abc, 123, etc.)
        sequential_patterns = [
            'abcdefghijklmnopqrstuvwxyz',
            '0123456789',
            'qwertyuiop',
            'asdfghjkl',
            'zxcvbnm'
        ]
        
        password_lower = password.lower()
        
        for pattern in sequential_patterns:
            for i in range(len(pattern) - self.max_sequential + 1):
                sequence = pattern[i:i + self.max_sequential]
                if sequence in password_lower:
                    raise ValidationError(
                        _('Password cannot contain sequential characters (like "abc" or "123").')
                    )
                    
                # Check reverse sequence
                reverse_sequence = sequence[::-1]
                if reverse_sequence in password_lower:
                    raise ValidationError(
                        _('Password cannot contain sequential characters (like "cba" or "321").')
                    )
    
    def get_help_text(self):
        return _('Your password cannot contain sequential characters.')


class NoRepeatedCharactersValidator:
    """
    Validates that the password doesn't contain too many repeated characters.
    """
    
    def __init__(self, max_repeated=2):
        self.max_repeated = max_repeated
        
    def validate(self, password, user=None):
        # Check for repeated characters
        for i in range(len(password) - self.max_repeated):
            if password[i] == password[i + 1] == password[i + 2]:
                raise ValidationError(
                    _('Password cannot contain more than %(max)d consecutive identical characters.') % {'max': self.max_repeated}
                )
    
    def get_help_text(self):
        return _(
            'Your password cannot contain more than %(max)d consecutive identical characters.'
        ) % {'max': self.max_repeated} 