# --- Standard Library Imports ---
import os
import uuid
from typing import Optional, Dict, Any, Tuple
from django.utils.translation import gettext_lazy as _
from django.core.files.uploadedfile import InMemoryUploadedFile
from django.core.exceptions import ValidationError
from PIL import Image, ImageOps, ExifTags
import io
import sys
from ....constants import ModelConstants, ValidationMessages


# --- Image Processing Functions ---

def validate_image_file(image_file) -> None:
    """
    Validates uploaded image file for size and format.
    
    Args:
        image_file: Django uploaded file object
        
    Raises:
        ValidationError: If image is invalid
    """
    if not image_file:
        return
        
    # Check file size
    if image_file.size > ModelConstants.MAX_IMAGE_SIZE_MB * 1024 * 1024:
        raise ValidationError(ValidationMessages.IMAGE_TOO_LARGE)
    
    # Check file format
    try:
        with Image.open(image_file) as img:
            if img.format not in ModelConstants.ALLOWED_IMAGE_FORMATS:
                raise ValidationError(ValidationMessages.INVALID_IMAGE_FORMAT)
    except Exception:
        raise ValidationError(ValidationMessages.INVALID_IMAGE_FORMAT)


def process_profile_image(image_file, max_width: int = 400, max_height: int = 400, quality: int = 85) -> Optional[InMemoryUploadedFile]:
    """
    Processes and optimizes profile image with proper orientation and resizing.
    
    Args:
        image_file: Django uploaded file object
        max_width: Maximum width in pixels
        max_height: Maximum height in pixels
        quality: JPEG quality (1-100)
        
    Returns:
        Optional[InMemoryUploadedFile]: Processed image file or None
    """
    if not image_file:
        return None
        
    # Open and process the image
    with Image.open(image_file) as img:
        # Fix orientation based on EXIF data
        try:
            exif = img.getexif()
            if exif is not None:
                orientation_value = exif.get(274)  # 274 is the EXIF orientation tag
                if orientation_value == 3:
                    img = img.rotate(180, expand=True)
                elif orientation_value == 6:
                    img = img.rotate(270, expand=True)
                elif orientation_value == 8:
                    img = img.rotate(90, expand=True)
        except (AttributeError, KeyError, TypeError):
            pass
        
        # Convert to RGB if necessary
        if img.mode in ('RGBA', 'LA', 'P'):
            background = Image.new('RGB', img.size, (255, 255, 255))
            background.paste(img, mask=img.split()[-1] if img.mode == 'RGBA' else None)
            img = background
        
        # Resize image while maintaining aspect ratio
        img.thumbnail((max_width, max_height), Image.Resampling.LANCZOS)
        
        # Save to BytesIO
        output = io.BytesIO()
        img.save(output, format='JPEG', quality=quality, optimize=True)
        output.seek(0)
        
        # Create new InMemoryUploadedFile
        return InMemoryUploadedFile(
            output,
            'ImageField',
            f"{image_file.name.split('.')[0]}_processed.jpg",
            'image/jpeg',
            sys.getsizeof(output),
            None
        )


def create_thumbnail(image_file, size: Tuple[int, int] = (150, 150)) -> Optional[InMemoryUploadedFile]:
    """
    Creates a thumbnail version of the image.
    
    Args:
        image_file: Django uploaded file object
        size: Tuple of (width, height) for thumbnail
        
    Returns:
        Optional[InMemoryUploadedFile]: Thumbnail image file or None
    """
    if not image_file:
        return None
        
    with Image.open(image_file) as img:
        # Convert to RGB if necessary
        if img.mode in ('RGBA', 'LA', 'P'):
            background = Image.new('RGB', img.size, (255, 255, 255))
            background.paste(img, mask=img.split()[-1] if img.mode == 'RGBA' else None)
            img = background
        
        # Create thumbnail
        img.thumbnail(size, Image.Resampling.LANCZOS)
        
        # Save to BytesIO
        output = io.BytesIO()
        img.save(output, format='JPEG', quality=90, optimize=True)
        output.seek(0)
        
        # Create new InMemoryUploadedFile
        return InMemoryUploadedFile(
            output,
            'ImageField',
            f"{image_file.name.split('.')[0]}_thumb.jpg",
            'image/jpeg',
            sys.getsizeof(output),
            None
        )


# --- Shared Profile Completion Functions ---

def get_profile_completion_suggestions(user) -> Dict[str, Any]:
    """
    Returns suggestions for completing user profile.
    
    Args:
        user: CustomUser instance
        
    Returns:
        Dict containing suggestions and priority actions
    """
    # Import here to avoid circular imports
    from ..customer.completion import calculate_customer_profile_completion
    from ..provider.completion import calculate_provider_profile_completion
    
    if user.is_customer:
        completion_data = calculate_customer_profile_completion(user)
    elif user.is_service_provider:
        completion_data = calculate_provider_profile_completion(user)
    else:
        return {'suggestions': [], 'priority_actions': []}
    
    # Define suggestions based on missing fields
    field_suggestions = {
        'first_name': {
            'message': _('Add your first name to personalize your profile'),
            'priority': 'high',
            'action': 'complete_basic_info'
        },
        'last_name': {
            'message': _('Add your last name to complete your identity'),
            'priority': 'high',
            'action': 'complete_basic_info'
        },
        'profile_picture': {
            'message': _('Upload a profile picture to make your profile more personal'),
            'priority': 'medium',
            'action': 'upload_photo'
        },
        'phone_number': {
            'message': _('Add your phone number for better communication'),
            'priority': 'medium',
            'action': 'add_contact_info'
        },
        'address': {
            'message': _('Add your address for location-based services'),
            'priority': 'medium',
            'action': 'add_location'
        },
        'email_verified': {
            'message': _('Verify your email address to secure your account'),
            'priority': 'critical',
            'action': 'verify_email'
        },
        'legal_name': {
            'message': _('Add your business legal name (required for providers)'),
            'priority': 'critical',
            'action': 'complete_business_info'
        },
        'logo': {
            'message': _('Upload your business logo to build brand recognition'),
            'priority': 'medium',
            'action': 'upload_logo'
        },
        'description': {
            'message': _('Add a business description to attract customers'),
            'priority': 'high',
            'action': 'complete_business_info'
        },
    }
    
    suggestions = []
    priority_actions = []
    
    for field in completion_data['missing_fields']:
        if field in field_suggestions:
            suggestion = field_suggestions[field]
            suggestions.append(suggestion)
            
            if suggestion['priority'] == 'critical':
                priority_actions.append(suggestion)
    
    # Sort suggestions by priority
    priority_order = {'critical': 0, 'high': 1, 'medium': 2, 'low': 3}
    suggestions.sort(key=lambda x: priority_order.get(x['priority'], 3))
    
    return {
        'suggestions': suggestions,
        'priority_actions': priority_actions,
        'completion_percentage': completion_data['percentage'],
        'missing_fields_count': len(completion_data['missing_fields'])
    }


# --- Shared File Path Generators ---

def _generate_filename(prefix: str, identifier: Optional[str], filename: str) -> str:
    ext = filename.split('.')[-1]
    unique_id = uuid.uuid4().hex
    identifier_part = f"{identifier}_" if identifier else ""
    return f"{prefix}_{identifier_part}{unique_id}.{ext}"

def get_profile_thumbnail_path(instance, filename: str) -> str:
    """Generate path for profile picture thumbnails"""
    return os.path.join('thumbnails', 'profile_images', _generate_filename('thumb', str(instance.user.id), filename)) 