# --- Standard Library Imports ---
import io
import sys
from typing import Op<PERSON>, Tuple
from django.core.files.uploadedfile import InMemoryUploadedFile
from PIL import Image, ImageOps, ExifTags
from ....constants import ModelConstants


def process_profile_image(image_file, max_width: int = 400, max_height: int = 400, quality: int = 85) -> Optional[InMemoryUploadedFile]:
    """
    Processes and optimizes profile image with proper orientation and resizing.
    
    Args:
        image_file: Django uploaded file object
        max_width: Maximum width in pixels
        max_height: Maximum height in pixels
        quality: JPEG quality (1-100)
        
    Returns:
        Optional[InMemoryUploadedFile]: Processed image file or None
    """
    if not image_file:
        return None
        
    # Open and process the image
    with Image.open(image_file) as img:
        # Fix orientation based on EXIF data
        try:
            exif = img.getexif()
            if exif is not None:
                orientation_value = exif.get(274)  # 274 is the EXIF orientation tag
                if orientation_value == 3:
                    img = img.rotate(180, expand=True)
                elif orientation_value == 6:
                    img = img.rotate(270, expand=True)
                elif orientation_value == 8:
                    img = img.rotate(90, expand=True)
        except (AttributeError, KeyError, TypeError):
            pass
        
        # Convert to RGB if necessary
        if img.mode in ('RGBA', 'LA', 'P'):
            background = Image.new('RGB', img.size, (255, 255, 255))
            background.paste(img, mask=img.split()[-1] if img.mode == 'RGBA' else None)
            img = background
        
        # Resize image while maintaining aspect ratio
        img.thumbnail((max_width, max_height), Image.Resampling.LANCZOS)
        
        # Save to BytesIO
        output = io.BytesIO()
        img.save(output, format='JPEG', quality=quality, optimize=True)
        output.seek(0)
        
        # Create new InMemoryUploadedFile
        return InMemoryUploadedFile(
            output,
            'ImageField',
            f"{image_file.name.split('.')[0]}_processed.jpg",
            'image/jpeg',
            sys.getsizeof(output),
            None
        )


def create_thumbnail(image_file, size: Tuple[int, int] = (150, 150)) -> Optional[InMemoryUploadedFile]:
    """
    Creates a thumbnail version of the image.
    
    Args:
        image_file: Django uploaded file object
        size: Tuple of (width, height) for thumbnail
        
    Returns:
        Optional[InMemoryUploadedFile]: Thumbnail image file or None
    """
    if not image_file:
        return None
        
    with Image.open(image_file) as img:
        # Convert to RGB if necessary
        if img.mode in ('RGBA', 'LA', 'P'):
            background = Image.new('RGB', img.size, (255, 255, 255))
            background.paste(img, mask=img.split()[-1] if img.mode == 'RGBA' else None)
            img = background
        
        # Create thumbnail
        img.thumbnail(size, Image.Resampling.LANCZOS)
        
        # Save to BytesIO
        output = io.BytesIO()
        img.save(output, format='JPEG', quality=90, optimize=True)
        output.seek(0)
        
        # Create new InMemoryUploadedFile
        return InMemoryUploadedFile(
            output,
            'ImageField',
            f"{image_file.name.split('.')[0]}_thumb.jpg",
            'image/jpeg',
            sys.getsizeof(output),
            None
        ) 