"""
Constants package for accounts_app.
This module provides access to all constants organized by functionality.
"""

# Import common constants
from .common import (
    UserRoles,
    UserStatus,
    Gender,
    Months,
    USStates,
    AlertTypes,
    SeverityLevels,
    ThemePreferences,
    LanguagePreferences,
    TimezonePreferences,
    NotificationPreferences,
    SecuritySettings,
    RegexPatterns,
    ModelConstants,
    ValidationMessages,
    DefaultValues,
)

# Import customer-specific constants
from .customer import (
    CustomerProfileTypes,
    CustomerPreferences,
    CustomerAccountStatus,
    CustomerVerificationStatus,
    CustomerPrivacySettings,
    CustomerDefaultValues,
    CustomerValidationMessages,
    CustomerModelConstants,
)

# Import provider-specific constants
from .provider import (
    ProviderBusinessTypes,
    ProviderServiceCategories,
    ProviderVerificationStatus,
    ProviderAccountStatus,
    ProviderBusinessHours,
    ProviderPaymentMethods,
    ProviderInsuranceTypes,
    ProviderDefaultValues,
    ProviderValidationMessages,
    ProviderModelConstants,
)

# Export all constants for backward compatibility
__all__ = [
    # Common constants
    'UserRoles',
    'UserStatus',
    'Gender',
    'Months',
    'USStates',
    'AlertTypes',
    'SeverityLevels',
    'ThemePreferences',
    'LanguagePreferences',
    'TimezonePreferences',
    'NotificationPreferences',
    'SecuritySettings',
    'RegexPatterns',
    'ModelConstants',
    'ValidationMessages',
    'DefaultValues',
    
    # Customer constants
    'CustomerProfileTypes',
    'CustomerPreferences',
    'CustomerAccountStatus',
    'CustomerVerificationStatus',
    'CustomerPrivacySettings',
    'CustomerDefaultValues',
    'CustomerValidationMessages',
    'CustomerModelConstants',
    
    # Provider constants
    'ProviderBusinessTypes',
    'ProviderServiceCategories',
    'ProviderVerificationStatus',
    'ProviderAccountStatus',
    'ProviderBusinessHours',
    'ProviderPaymentMethods',
    'ProviderInsuranceTypes',
    'ProviderDefaultValues',
    'ProviderValidationMessages',
    'ProviderModelConstants',
] 