"""
Customer-specific constants for the accounts_app.
This module contains constants that are specific to customer functionality.
"""

from django.utils.translation import gettext_lazy as _


# --- Customer Profile Types ---
class CustomerProfileTypes:
    INDIVIDUAL = 'individual'
    FAMILY = 'family'
    BUSINESS = 'business'
    
    CHOICES = (
        (INDIVIDUAL, _('Individual')),
        (FAMILY, _('Family')),
        (BUSINESS, _('Business')),
    )


# --- Customer Preferences ---
class CustomerPreferences:
    # Booking preferences
    AUTO_CONFIRM_BOOKINGS = 'auto_confirm'
    MANUAL_CONFIRM_BOOKINGS = 'manual_confirm'
    
    BOOKING_CONFIRMATION_CHOICES = (
        (AUTO_CONFIRM_BOOKINGS, _('Auto-confirm bookings')),
        (MANUAL_CONFIRM_BOOKINGS, _('Manual confirmation required')),
    )
    
    # Communication preferences
    EMAIL_NOTIFICATIONS = 'email'
    SMS_NOTIFICATIONS = 'sms'
    PUSH_NOTIFICATIONS = 'push'
    ALL_NOTIFICATIONS = 'all'
    NO_NOTIFICATIONS = 'none'
    
    NOTIFICATION_CHOICES = (
        (EMAIL_NOTIFICATIONS, _('Email only')),
        (SMS_NOTIFICATIONS, _('SMS only')),
        (PUSH_NOTIFICATIONS, _('Push notifications only')),
        (ALL_NOTIFICATIONS, _('All notifications')),
        (NO_NOTIFICATIONS, _('No notifications')),
    )


# --- Customer Account Status ---
class CustomerAccountStatus:
    ACTIVE = 'active'
    INACTIVE = 'inactive'
    SUSPENDED = 'suspended'
    PENDING_VERIFICATION = 'pending_verification'
    LIMITED = 'limited'
    
    CHOICES = (
        (ACTIVE, _('Active')),
        (INACTIVE, _('Inactive')),
        (SUSPENDED, _('Suspended')),
        (PENDING_VERIFICATION, _('Pending Verification')),
        (LIMITED, _('Limited Access')),
    )


# --- Customer Verification Status ---
class CustomerVerificationStatus:
    UNVERIFIED = 'unverified'
    EMAIL_VERIFIED = 'email_verified'
    PHONE_VERIFIED = 'phone_verified'
    ID_VERIFIED = 'id_verified'
    FULLY_VERIFIED = 'fully_verified'
    
    CHOICES = (
        (UNVERIFIED, _('Unverified')),
        (EMAIL_VERIFIED, _('Email Verified')),
        (PHONE_VERIFIED, _('Phone Verified')),
        (ID_VERIFIED, _('ID Verified')),
        (FULLY_VERIFIED, _('Fully Verified')),
    )


# --- Customer Privacy Settings ---
class CustomerPrivacySettings:
    PUBLIC = 'public'
    PRIVATE = 'private'
    FRIENDS_ONLY = 'friends_only'
    SELECTIVE = 'selective'
    
    CHOICES = (
        (PUBLIC, _('Public')),
        (PRIVATE, _('Private')),
        (FRIENDS_ONLY, _('Friends Only')),
        (SELECTIVE, _('Selective')),
    )


# --- Customer Default Values ---
class CustomerDefaultValues:
    DEFAULT_PROFILE_TYPE = CustomerProfileTypes.INDIVIDUAL
    DEFAULT_ACCOUNT_STATUS = CustomerAccountStatus.ACTIVE
    DEFAULT_VERIFICATION_STATUS = CustomerVerificationStatus.UNVERIFIED
    DEFAULT_PRIVACY_SETTING = CustomerPrivacySettings.PUBLIC
    DEFAULT_BOOKING_CONFIRMATION = CustomerPreferences.AUTO_CONFIRM_BOOKINGS
    DEFAULT_NOTIFICATION_PREFERENCE = CustomerPreferences.EMAIL_NOTIFICATIONS
    DEFAULT_IS_PUBLIC = True
    DEFAULT_IS_ACTIVE = True


# --- Customer Validation Messages ---
class CustomerValidationMessages:
    INVALID_BIRTH_DATE = _('Please enter a valid birth date.')
    INVALID_AGE = _('You must be at least 13 years old to register.')
    INVALID_PHONE_FORMAT = _('Please enter a valid phone number.')
    INVALID_ADDRESS = _('Please enter a valid address.')
    INVALID_ZIP_CODE = _('Please enter a valid ZIP code.')
    INVALID_PROFILE_TYPE = _('Please select a valid profile type.')
    INVALID_PRIVACY_SETTING = _('Please select a valid privacy setting.')
    INVALID_NOTIFICATION_PREFERENCE = _('Please select a valid notification preference.')
    INVALID_BOOKING_CONFIRMATION = _('Please select a valid booking confirmation preference.')
    PROFILE_ALREADY_EXISTS = _('A profile already exists for this user.')
    PROFILE_NOT_FOUND = _('Profile not found for this user.')
    INVALID_PROFILE_DATA = _('Invalid profile data provided.')
    PROFILE_UPDATE_FAILED = _('Failed to update profile. Please try again.')
    PROFILE_CREATION_FAILED = _('Failed to create profile. Please try again.')
    INVALID_PREFERENCE_VALUE = _('Invalid preference value provided.')
    PREFERENCE_UPDATE_FAILED = _('Failed to update preferences. Please try again.')


# --- Customer Model Constants ---
class CustomerModelConstants:
    # Field lengths
    FIRST_NAME_MAX_LENGTH = 50
    LAST_NAME_MAX_LENGTH = 50
    PHONE_MAX_LENGTH = 20
    ADDRESS_MAX_LENGTH = 255
    CITY_MAX_LENGTH = 100
    STATE_MAX_LENGTH = 2
    ZIP_CODE_MAX_LENGTH = 10
    BIO_MAX_LENGTH = 500
    PREFERENCES_MAX_LENGTH = 1000
    
    # Age limits
    MIN_AGE = 13
    MAX_AGE = 120
    
    # Profile completion thresholds
    MIN_PROFILE_COMPLETION = 50
    OPTIMAL_PROFILE_COMPLETION = 80
    
    # File upload settings
    MAX_PROFILE_IMAGE_SIZE_MB = 5
    ALLOWED_PROFILE_IMAGE_FORMATS = ['JPEG', 'PNG', 'GIF', 'WEBP']
    
    # Session settings
    CUSTOMER_SESSION_TIMEOUT_SECONDS = 3600  # 1 hour
    CUSTOMER_REMEMBER_ME_DAYS = 30 