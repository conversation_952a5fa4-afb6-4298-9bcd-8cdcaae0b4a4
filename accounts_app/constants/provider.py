"""
Provider-specific constants for the accounts_app.
This module contains constants that are specific to service provider functionality.
"""

from django.utils.translation import gettext_lazy as _


# --- Provider Business Types ---
class ProviderBusinessTypes:
    INDIVIDUAL = 'individual'
    PARTNERSHIP = 'partnership'
    CORPORATION = 'corporation'
    LLC = 'llc'
    NONPROFIT = 'nonprofit'
    GOVERNMENT = 'government'
    
    CHOICES = (
        (INDIVIDUAL, _('Individual')),
        (PARTNERSHIP, _('Partnership')),
        (CORPORATION, _('Corporation')),
        (LLC, _('Limited Liability Company')),
        (NONPROFIT, _('Non-Profit Organization')),
        (GOVERNMENT, _('Government Entity')),
    )


# --- Provider Service Categories ---
class ProviderServiceCategories:
    VENUE_RENTAL = 'venue_rental'
    CATERING = 'catering'
    PHOTOGRAPHY = 'photography'
    MUSIC = 'music'
    DECORATION = 'decoration'
    TRANSPORTATION = 'transportation'
    ENTERTAINMENT = 'entertainment'
    PLANNING = 'planning'
    OTHER = 'other'
    
    CHOICES = (
        (VENUE_RENTAL, _('Venue Rental')),
        (CATERING, _('Catering')),
        (PHOTOGRAPHY, _('Photography')),
        (MUSIC, _('Music & Entertainment')),
        (DECORATION, _('Decoration & Design')),
        (TRANSPORTATION, _('Transportation')),
        (ENTERTAINMENT, _('Entertainment')),
        (PLANNING, _('Event Planning')),
        (OTHER, _('Other Services')),
    )


# --- Provider Verification Status ---
class ProviderVerificationStatus:
    UNVERIFIED = 'unverified'
    EMAIL_VERIFIED = 'email_verified'
    PHONE_VERIFIED = 'phone_verified'
    BUSINESS_VERIFIED = 'business_verified'
    DOCUMENTS_VERIFIED = 'documents_verified'
    FULLY_VERIFIED = 'fully_verified'
    
    CHOICES = (
        (UNVERIFIED, _('Unverified')),
        (EMAIL_VERIFIED, _('Email Verified')),
        (PHONE_VERIFIED, _('Phone Verified')),
        (BUSINESS_VERIFIED, _('Business Verified')),
        (DOCUMENTS_VERIFIED, _('Documents Verified')),
        (FULLY_VERIFIED, _('Fully Verified')),
    )


# --- Provider Account Status ---
class ProviderAccountStatus:
    ACTIVE = 'active'
    INACTIVE = 'inactive'
    SUSPENDED = 'suspended'
    PENDING_VERIFICATION = 'pending_verification'
    LIMITED = 'limited'
    APPROVED = 'approved'
    REJECTED = 'rejected'
    
    CHOICES = (
        (ACTIVE, _('Active')),
        (INACTIVE, _('Inactive')),
        (SUSPENDED, _('Suspended')),
        (PENDING_VERIFICATION, _('Pending Verification')),
        (LIMITED, _('Limited Access')),
        (APPROVED, _('Approved')),
        (REJECTED, _('Rejected')),
    )


# --- Provider Business Hours ---
class ProviderBusinessHours:
    MONDAY = 'monday'
    TUESDAY = 'tuesday'
    WEDNESDAY = 'wednesday'
    THURSDAY = 'thursday'
    FRIDAY = 'friday'
    SATURDAY = 'saturday'
    SUNDAY = 'sunday'
    
    CHOICES = (
        (MONDAY, _('Monday')),
        (TUESDAY, _('Tuesday')),
        (WEDNESDAY, _('Wednesday')),
        (THURSDAY, _('Thursday')),
        (FRIDAY, _('Friday')),
        (SATURDAY, _('Saturday')),
        (SUNDAY, _('Sunday')),
    )


# --- Provider Payment Methods ---
class ProviderPaymentMethods:
    CREDIT_CARD = 'credit_card'
    DEBIT_CARD = 'debit_card'
    BANK_TRANSFER = 'bank_transfer'
    PAYPAL = 'paypal'
    VENMO = 'venmo'
    CASH = 'cash'
    CHECK = 'check'
    
    CHOICES = (
        (CREDIT_CARD, _('Credit Card')),
        (DEBIT_CARD, _('Debit Card')),
        (BANK_TRANSFER, _('Bank Transfer')),
        (PAYPAL, _('PayPal')),
        (VENMO, _('Venmo')),
        (CASH, _('Cash')),
        (CHECK, _('Check')),
    )


# --- Provider Insurance Types ---
class ProviderInsuranceTypes:
    GENERAL_LIABILITY = 'general_liability'
    PROFESSIONAL_LIABILITY = 'professional_liability'
    WORKERS_COMPENSATION = 'workers_compensation'
    PROPERTY_INSURANCE = 'property_insurance'
    AUTO_INSURANCE = 'auto_insurance'
    NONE = 'none'
    
    CHOICES = (
        (GENERAL_LIABILITY, _('General Liability')),
        (PROFESSIONAL_LIABILITY, _('Professional Liability')),
        (WORKERS_COMPENSATION, _('Workers Compensation')),
        (PROPERTY_INSURANCE, _('Property Insurance')),
        (AUTO_INSURANCE, _('Auto Insurance')),
        (NONE, _('No Insurance')),
    )


# --- Provider Default Values ---
class ProviderDefaultValues:
    DEFAULT_BUSINESS_TYPE = ProviderBusinessTypes.INDIVIDUAL
    DEFAULT_ACCOUNT_STATUS = ProviderAccountStatus.PENDING_VERIFICATION
    DEFAULT_VERIFICATION_STATUS = ProviderVerificationStatus.UNVERIFIED
    DEFAULT_IS_PUBLIC = True
    DEFAULT_IS_ACTIVE = True
    DEFAULT_IS_FEATURED = False
    DEFAULT_IS_VERIFIED = False
    DEFAULT_ACCEPTING_BOOKINGS = True
    DEFAULT_AUTO_CONFIRM_BOOKINGS = False
    DEFAULT_REQUIRE_DEPOSIT = False
    DEFAULT_DEPOSIT_PERCENTAGE = 25


# --- Provider Validation Messages ---
class ProviderValidationMessages:
    INVALID_BUSINESS_NAME = _('Business name must be at least 3 characters long.')
    INVALID_CONTACT_NAME = _('Contact name must be at least 2 characters long.')
    INVALID_BUSINESS_TYPE = _('Please select a valid business type.')
    INVALID_SERVICE_CATEGORY = _('Please select a valid service category.')
    INVALID_VERIFICATION_STATUS = _('Please select a valid verification status.')
    INVALID_ACCOUNT_STATUS = _('Please select a valid account status.')
    INVALID_BUSINESS_HOURS = _('Business hours must be in valid format.')
    INVALID_PAYMENT_METHOD = _('Please select a valid payment method.')
    INVALID_INSURANCE_TYPE = _('Please select a valid insurance type.')
    INVALID_WEBSITE_URL = _('Please enter a valid website URL.')
    INVALID_SOCIAL_MEDIA_URL = _('Please enter a valid social media URL.')
    INVALID_LICENSE_NUMBER = _('Please enter a valid license number.')
    INVALID_TAX_ID = _('Please enter a valid tax ID.')
    INVALID_BUSINESS_ADDRESS = _('Please enter a valid business address.')
    INVALID_BUSINESS_PHONE = _('Please enter a valid business phone number.')
    INVALID_DESCRIPTION = _('Description must be at least 10 characters long.')
    INVALID_PRICE_RANGE = _('Please enter a valid price range.')
    INVALID_CAPACITY = _('Please enter a valid capacity number.')
    INVALID_YEARS_IN_BUSINESS = _('Please enter a valid number of years in business.')
    PROFILE_ALREADY_EXISTS = _('A provider profile already exists for this user.')
    PROFILE_NOT_FOUND = _('Provider profile not found for this user.')
    INVALID_PROFILE_DATA = _('Invalid provider profile data provided.')
    PROFILE_UPDATE_FAILED = _('Failed to update provider profile. Please try again.')
    PROFILE_CREATION_FAILED = _('Failed to create provider profile. Please try again.')
    INVALID_DOCUMENT_UPLOAD = _('Invalid document uploaded. Please check the file format and size.')
    DOCUMENT_UPLOAD_FAILED = _('Failed to upload document. Please try again.')
    INVALID_CERTIFICATION = _('Please enter a valid certification.')
    INVALID_AWARD = _('Please enter a valid award.')
    INVALID_REFERENCE = _('Please enter a valid reference.')


# --- Provider Model Constants ---
class ProviderModelConstants:
    # Field lengths
    BUSINESS_NAME_MAX_LENGTH = 100
    CONTACT_NAME_MAX_LENGTH = 100
    BUSINESS_PHONE_MAX_LENGTH = 20
    BUSINESS_ADDRESS_MAX_LENGTH = 255
    BUSINESS_CITY_MAX_LENGTH = 100
    BUSINESS_STATE_MAX_LENGTH = 2
    BUSINESS_ZIP_CODE_MAX_LENGTH = 10
    WEBSITE_URL_MAX_LENGTH = 200
    SOCIAL_MEDIA_URL_MAX_LENGTH = 200
    LICENSE_NUMBER_MAX_LENGTH = 50
    TAX_ID_MAX_LENGTH = 20
    DESCRIPTION_MAX_LENGTH = 1000
    BIO_MAX_LENGTH = 500
    SPECIALTIES_MAX_LENGTH = 500
    CERTIFICATIONS_MAX_LENGTH = 1000
    AWARDS_MAX_LENGTH = 1000
    REFERENCES_MAX_LENGTH = 1000
    
    # Business settings
    MIN_YEARS_IN_BUSINESS = 0
    MAX_YEARS_IN_BUSINESS = 100
    MIN_CAPACITY = 1
    MAX_CAPACITY = 10000
    MIN_PRICE = 0
    MAX_PRICE = 1000000
    
    # File upload settings
    MAX_PROFILE_IMAGE_SIZE_MB = 5
    MAX_DOCUMENT_SIZE_MB = 10
    ALLOWED_PROFILE_IMAGE_FORMATS = ['JPEG', 'PNG', 'GIF', 'WEBP']
    ALLOWED_DOCUMENT_FORMATS = ['PDF', 'DOC', 'DOCX', 'JPEG', 'PNG']
    
    # Session settings
    PROVIDER_SESSION_TIMEOUT_SECONDS = 3600  # 1 hour
    PROVIDER_REMEMBER_ME_DAYS = 30
    
    # Verification settings
    MIN_DOCUMENTS_REQUIRED = 1
    MAX_DOCUMENTS_ALLOWED = 10
    VERIFICATION_EXPIRY_DAYS = 365 