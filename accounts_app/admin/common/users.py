# --- Standard Library Imports ---
from datetime import timedelta

# --- Django Imports ---
from django.contrib import admin, messages
from django.contrib.auth.admin import UserAdmin
from django.contrib.auth.tokens import default_token_generator
from django.utils.encoding import force_bytes
from django.utils.http import urlsafe_base64_encode
from django.utils.translation import gettext_lazy as _
from django.urls import reverse

# --- Local Imports ---
from ...models import CustomUser, LoginHistory


@admin.register(CustomUser)
class CustomUserAdmin(UserAdmin):
    """
    Admin interface for CustomUser with bulk actions and status indicators.
    """
    list_display = (
        'email', 'role', 'get_full_name', 'is_active', 'is_staff',
        'date_joined', 'last_login'
    )
    list_filter = ('role', 'is_active', 'is_staff', 'date_joined')
    search_fields = ('email', 'first_name', 'last_name')
    ordering = ('email',)
    readonly_fields = ('last_login', 'date_joined')

    fieldsets = (
        (None, {'fields': ('email', 'password')}),
        (_('Personal info'), {'fields': ('first_name', 'last_name')}),
        (_('Permissions'), {
            'fields': (
                'is_active', 'is_staff', 'is_superuser',
                'groups', 'user_permissions'
            )
        }),
        (_('Important dates'), {'fields': ('last_login', 'date_joined')}),
    )
    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': ('email', 'password1', 'password2'),
        }),
    )

    actions = [
        'activate_users',
        'deactivate_users',
        'clear_failed_login_attempts',
        'reset_passwords',
    ]

    def activate_users(self, request, queryset):
        updated = queryset.update(is_active=True)
        self.message_user(request, f'{updated} user(s) activated.', messages.SUCCESS)
    activate_users.short_description = 'Activate selected users'

    def deactivate_users(self, request, queryset):
        updated = queryset.update(is_active=False)
        self.message_user(request, f'{updated} user(s) deactivated.', messages.SUCCESS)
    deactivate_users.short_description = 'Deactivate selected users'

    def clear_failed_login_attempts(self, request, queryset):
        total_cleared = 0
        for user in queryset:
            cleared, _ = LoginHistory.objects.filter(
                user=user, is_successful=False
            ).delete()
            total_cleared += cleared
        self.message_user(
            request,
            f'Cleared failed attempts for {total_cleared} user(s).',
            messages.SUCCESS
        )
    clear_failed_login_attempts.short_description = 'Clear failed login attempts'

    def reset_passwords(self, request, queryset):
        for user in queryset:
            token = default_token_generator.make_token(user)
            uid = urlsafe_base64_encode(force_bytes(user.pk))
            url = reverse(
                'password_reset_confirm',
                kwargs={'uidb64': uid, 'token': token}
            )
            full_url = request.build_absolute_uri(url)
            # TODO: send reset link (full_url) to user.email
        self.message_user(request, 'Password reset emails sent.', messages.SUCCESS)
    reset_passwords.short_description = 'Send password reset email' 