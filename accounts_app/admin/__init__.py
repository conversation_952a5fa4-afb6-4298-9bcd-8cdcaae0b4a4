# Import all admin classes from the organized modules

# Common admin classes (shared across user types)
from .common.users import CustomUserAdmin
from .common.security import (
    LoginHistoryAdmin,
    LoginAlertAdmin,
)
from .common.privacy import (
    ProfilePrivacySettingsAdmin,
    ProfileVerificationAdmin,
)

# Customer-specific admin classes
from .customer.profiles import CustomerProfileAdmin

# Provider-specific admin classes
from .provider.profiles import ServiceProviderProfileAdmin
from .provider.team import TeamMemberAdmin

# Make all admin classes available at package level
__all__ = [
    'CustomUserAdmin',
    'CustomerProfileAdmin',
    'ServiceProviderProfileAdmin',
    'LoginHistoryAdmin',
    'LoginAlertAdmin',
    'TeamMemberAdmin',
    'ProfilePrivacySettingsAdmin',
    'ProfileVerificationAdmin',
] 