# --- Django Imports ---
from django.contrib import admin
from django.utils.translation import gettext_lazy as _

# --- Local Imports ---
from ...models import ServiceProviderProfile


@admin.register(ServiceProviderProfile)
class ServiceProviderProfileAdmin(admin.ModelAdmin):
    """Manage service provider profiles."""
    list_display = (
        'business_name', 'user', 'city', 'state',
        'is_public', 'created'
    )
    list_filter = ('state', 'is_public', 'created')
    search_fields = (
        'legal_name', 'display_name', 'user__email',
        'city', 'phone'
    )
    readonly_fields = ('created', 'updated')
    fieldsets = (
        (_('User'), {'fields': ('user',)}),
        (_('Business'), {'fields': (
            'legal_name', 'display_name', 'description',
            'logo', 'ein'
        )}),
        (_('Contact'), {'fields': (
            'phone', 'contact_name'
        )}),
        (_('Address'), {'fields': (
            'address', 'city', 'state',
            'county', 'zip_code'
        )}),
        (_('Web'), {'fields': (
            'website', 'instagram', 'facebook'
        )}),
        (_('Settings'), {'fields': ('is_public',)}),
        (_('Metadata'), {'fields': ('created', 'updated')}),
    ) 