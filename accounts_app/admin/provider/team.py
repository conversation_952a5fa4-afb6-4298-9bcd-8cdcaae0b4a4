# --- Django Imports ---
from django.contrib import admin
from django.utils.translation import gettext_lazy as _

# --- Local Imports ---
from ...models import TeamMember


@admin.register(TeamMember)
class TeamMemberAdmin(admin.ModelAdmin):
    """Manage service provider team members."""
    list_display = (
        'name', 'position', 'service_provider',
        'is_active', 'created'
    )
    list_filter = ('is_active', 'position', 'created')
    search_fields = (
        'name', 'position',
        'service_provider__legal_name'
    )
    readonly_fields = ('created', 'updated')
    fieldsets = (
        (_('Service Provider'), {'fields': ('service_provider',)}),
        (_('Staff Info'), {'fields': (
            'name', 'position', 'photo', 'is_active'
        )}),
        (_('Metadata'), {'fields': ('created', 'updated')}),
    ) 