# --- Django Imports ---
from django import forms
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _

# --- Crispy Forms Imports ---
from crispy_forms.helper import FormHelper
from crispy_forms.layout import Layout, Field, Row, Column, HTML, Submit
from crispy_forms.bootstrap import FormActions

# --- Third-Party Imports (Optional Utils) ---
try:
    from utils.forms import ProfileImageForm
    from utils.image_service import ImageService
except ImportError:
    ProfileImageForm = None
    ImageService = None

# --- Local App Imports ---
from ..common import AccessibleFormMixin
from ...fields import PhoneNumberField
from ...models.customer import CustomerProfile
from ...constants import Gender
from ...utils import log_error, log_info
from ...mixins import PhoneValidationMixin, ImageValidationMixin


class CustomerProfileForm(AccessibleFormMixin, PhoneValidationMixin, ImageValidationMixin, forms.ModelForm):
    """
    Edit customer profile personal information.

    Features:
    - Date of birth selection
    - Phone number normalization
    - Profile picture processing
    """
    MONTH_CHOICES = [
        ('', _('Select Month')),
        (1, _('January')), (2, _('February')), (3, _('March')),
        (4, _('April')), (5, _('May')), (6, _('June')),
        (7, _('July')), (8, _('August')), (9, _('September')),
        (10, _('October')), (11, _('November')), (12, _('December')),
    ]
    YEAR_CHOICES = [('', _('Select Year'))] + [
        (year, str(year)) for year in range(1920, 2010)
    ]

    first_name = forms.CharField(
        label=_('First Name'),
        max_length=100, required=False,
        widget=forms.TextInput(
            attrs={
                'placeholder': _('Enter your first name'),
            }
        )
    )
    last_name = forms.CharField(
        label=_('Last Name'),
        max_length=100, required=False,
        widget=forms.TextInput(
            attrs={
                'placeholder': _('Enter your last name'),
            }
        )
    )
    phone_number = PhoneNumberField(
        label=_('Phone Number'),
        max_length=20, required=False,
        widget=forms.TextInput(
            attrs={
                'placeholder': '+****************',
                'type': 'tel',
            }
        ),
        help_text=_('Format: +****************')
    )
    gender = forms.ChoiceField(
        label=_('Gender'),
        choices=[('', _('Select Gender'))] +
                list(Gender.CHOICES),
        required=False,
        widget=forms.Select()
    )
    birth_month = forms.ChoiceField(
        label=_('Birth Month'),
        choices=MONTH_CHOICES, required=False,
        widget=forms.Select()
    )
    birth_year = forms.ChoiceField(
        label=_('Birth Year'),
        choices=YEAR_CHOICES, required=False,
        widget=forms.Select()
    )
    address = forms.CharField(
        label=_('Address'), max_length=255, required=False,
        widget=forms.TextInput(
            attrs={
                'placeholder': _('Enter your address'),
            }
        )
    )
    city = forms.CharField(
        label=_('City'), max_length=100, required=False,
        widget=forms.TextInput(
            attrs={
                'placeholder': _('Enter your city'),
            }
        )
    )
    zip_code = forms.CharField(
        label=_('ZIP Code'), max_length=10, required=False,
        widget=forms.TextInput(
            attrs={
                'placeholder': _('Enter your ZIP code'),
            }
        )
    )
    profile_picture = forms.ImageField(
        label=_('Profile Picture'), required=False,
        widget=forms.FileInput(
            attrs={
                'accept': 'image/jpeg,image/png',
            }
        ),
        help_text=_(
            'Upload a profile picture (JPG or PNG only, max 5MB). '
            'Image will be resized to 800x800 pixels.'
        )
    )

    class Meta:
        model = CustomerProfile
        fields = [
            'first_name', 'last_name', 'phone_number', 'gender',
            'birth_month', 'birth_year', 'address', 'city',
            'profile_picture',
        ]

    def __init__(self, *args, **kwargs):
        """Initialize form with crispy-forms helper."""
        super().__init__(*args, **kwargs)
        
        # Configure crispy forms helper
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.form_class = 'needs-validation'
        self.helper.form_enctype = 'multipart/form-data'
        self.helper.layout = Layout(
            HTML('<h5 class="mb-3">Personal Information</h5>'),
            Row(
                Column(
                    Field('first_name', css_class='form-control'),
                    css_class='col-md-6'
                ),
                Column(
                    Field('last_name', css_class='form-control'),
                    css_class='col-md-6'
                ),
                css_class='mb-3'
            ),
            Row(
                Column(
                    Field('phone_number', css_class='form-control'),
                    css_class='col-md-6'
                ),
                Column(
                    Field('gender', css_class='form-select'),
                    css_class='col-md-6'
                ),
                css_class='mb-3'
            ),
            HTML('<h5 class="mb-3">Date of Birth</h5>'),
            Row(
                Column(
                    Field('birth_month', css_class='form-select'),
                    css_class='col-md-6'
                ),
                Column(
                    Field('birth_year', css_class='form-select'),
                    css_class='col-md-6'
                ),
                css_class='mb-3'
            ),
            HTML('<h5 class="mb-3">Address</h5>'),
            Field('address', css_class='form-control'),
            Field('city', css_class='form-control'),
            HTML('<h5 class="mb-3">Profile Picture</h5>'),
            Field('profile_picture', css_class='form-control'),
            FormActions(
                Submit('save_profile', _('Save Profile'), css_class='btn-primary'),
                css_class='d-grid gap-2'
            )
        )

    def clean_birth_month(self):
        """
        Convert empty birth_month selection to None.
        """
        month = self.cleaned_data.get('birth_month')
        return None if month == '' else month

    def clean_birth_year(self):
        """
        Convert empty birth_year selection to None.
        """
        year = self.cleaned_data.get('birth_year')
        return None if year == '' else year

    def clean_profile_picture(self):
        """
        Validate and process the uploaded profile picture.

        Returns:
            UploadedFile or None: The validated image or None.

        Raises:
            ValidationError: On invalid or oversized image.
        """
        image = self.cleaned_data.get('profile_picture')
        if not image:
            return None
        # Skip validation for existing images
        if hasattr(image, 'url') and not hasattr(image, 'content_type'):
            return image
        # Advanced processing if utilities available
        if ProfileImageForm:
            try:
                form = ProfileImageForm(
                    files={'image': image},
                    entity_type='customers',
                    entity_id=self.instance.user.id if self.instance else None
                )
                if not form.is_valid():
                    raise ValidationError(form.errors['image'])
                return image
            except ValidationError as e:
                log_error(
                    error_type='image_validation',
                    error_message='Invalid customer profile image',
                    user=self.instance.user if self.instance else None,
                    exception=e
                )
                raise
            except Exception as e:
                log_error(
                    error_type='image_processing',
                    error_message='Failed to process customer profile image',
                    user=self.instance.user if self.instance else None,
                    exception=e
                )
                raise ValidationError(
                    _('Invalid image file. Please upload JPG or PNG.')
                )
        # Basic fallback validation
        if image.size > 5 * 1024 * 1024:
            raise ValidationError(
                _('Image file too large. Maximum size is 5MB.'),
                code='file_too_large'
            )
        if hasattr(image, 'content_type'):
            allowed = ['image/jpeg', 'image/png']
            if image.content_type not in allowed:
                raise ValidationError(
                    _('Invalid image format. Please use JPG or PNG only.'),
                    code='invalid_format'
                )
        return image

    def save(self, commit=True):
        """
        Persist profile updates, processing picture when needed.

        Returns:
            CustomerProfile: The saved profile instance.
        """
        profile = super().save(commit=False)
        image = self.cleaned_data.get('profile_picture')
        # Process new image if utilities are available
        if image and ImageService and ProfileImageForm:
            try:
                log_info(
                    info_type='image_processing',
                    info_message=f'Starting profile image processing for user {profile.user.email}',
                    user=profile.user,
                    details={'image_name': image.name, 'image_size': image.size}
                )

                form = ProfileImageForm(
                    files={'image': image},
                    entity_type='customers',
                    entity_id=profile.user.id
                )
                if form.is_valid():
                    log_info(
                        info_type='image_processing',
                        info_message='Profile image form is valid, processing image',
                        user=profile.user
                    )
                    processed = form.process()
                    path, _ = ImageService.save_image(
                        processed, user=profile.user
                    )
                    profile.profile_picture = path
                    log_info(
                        info_type='image_processing',
                        info_message=f'Profile image saved successfully: {path}',
                        user=profile.user,
                        details={'saved_path': path}
                    )
                else:
                    log_error(
                        error_type='image_validation',
                        error_message='Profile image form validation failed',
                        user=profile.user,
                        details={'form_errors': form.errors}
                    )
            except Exception as e:
                log_error(
                    error_type='image_processing',
                    error_message='Failed to process profile image',
                    user=profile.user,
                    exception=e
                )
        if commit:
            profile.save()
        return profile 