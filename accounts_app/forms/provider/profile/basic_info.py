"""
Basic Business Information Form Fields

Contains the main ServiceProviderProfileForm class with basic business info fields
and form layout configuration.
"""

# --- Django Imports ---
from django import forms
from django.utils.translation import gettext_lazy as _

# --- Crispy Forms Imports ---
from crispy_forms.helper import FormHelper
from crispy_forms.layout import Layout, Field, Div, HTML, Submit, Row, Column, Fieldset
from crispy_forms.bootstrap import FormActions

# --- Third-Party Imports (Optional Utils) ---
try:
    from utils.forms import ProfileImageForm, ImageUploadForm
    from utils.image_service import ImageService
except ImportError:
    ProfileImageForm = None
    ImageUploadForm = None
    ImageService = None

# --- Local App Imports ---
from ....fields import PhoneNumberField
from ....utils import log_error
from ....models import ServiceProviderProfile
from ....utils import normalize_phone
from ...common import AccessibleFormMixin
from ...mixins import (
    PhoneValidationMixin, 
    ImageValidationMixin,
    BusinessValidationMixin,
    URLValidationMixin
)

# Import field definitions
from .business_info import get_business_info_fields
from .contact_info import get_contact_info_fields
from .social_media import get_social_media_fields

# Import validation methods
from .validation import (
    clean_logo, clean_phone, clean_website, clean_instagram, clean_facebook,
    clean_ein, clean_zip_code, clean_legal_name, clean_display_name,
    clean_contact_name, clean_county, clean_address, clean_city, clean_description
)


class ServiceProviderProfileForm(AccessibleFormMixin, PhoneValidationMixin, ImageValidationMixin, BusinessValidationMixin, URLValidationMixin, forms.ModelForm):
    """
    Edit service provider business profile details.

    Features:
    - Business info fields
    - Logo upload and processing
    - Visibility toggle
    """
    
    def __init__(self, *args, **kwargs):
        """Initialize form with crispy-forms helper and dynamic fields."""
        super().__init__(*args, **kwargs)
        
        # Add fields from other modules
        business_fields = get_business_info_fields()
        contact_fields = get_contact_info_fields()
        social_fields = get_social_media_fields()
        
        # Add all fields to the form
        for field_name, field in business_fields.items():
            setattr(self, field_name, field)
        
        for field_name, field in contact_fields.items():
            setattr(self, field_name, field)
            
        for field_name, field in social_fields.items():
            setattr(self, field_name, field)
        
        # Configure crispy forms helper
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.form_class = 'needs-validation'
        self.helper.form_enctype = 'multipart/form-data'
        self.helper.layout = Layout(
            Fieldset(
                _('Business Information'),
                Field('legal_name', css_class='form-control'),
                Field('display_name', css_class='form-control'),
                Field('description', css_class='form-control'),
                Field('phone', css_class='form-control'),
                Field('contact_name', css_class='form-control'),
                css_class='mb-4'
            ),
            Fieldset(
                _('Business Address'),
                Field('address', css_class='form-control'),
                Row(
                    Column(
                        Field('city', css_class='form-control'),
                        css_class='col-md-6'
                    ),
                    Column(
                        Field('state', css_class='form-select'),
                        css_class='col-md-6'
                    ),
                    css_class='mb-3'
                ),
                Row(
                    Column(
                        Field('county', css_class='form-control'),
                        css_class='col-md-6'
                    ),
                    Column(
                        Field('zip_code', css_class='form-control'),
                        css_class='col-md-6'
                    ),
                    css_class='mb-3'
                ),
                Field('ein', css_class='form-control'),
                css_class='mb-4'
            ),
            Fieldset(
                _('Online Presence'),
                Field('website', css_class='form-control'),
                Field('instagram', css_class='form-control'),
                Field('facebook', css_class='form-control'),
                css_class='mb-4'
            ),
            Fieldset(
                _('Branding & Settings'),
                Field('logo', css_class='form-control'),
                Div(
                    Field('is_public', css_class='form-check-input'),
                    css_class='form-check mb-3'
                ),
                css_class='mb-4'
            ),
            FormActions(
                Submit('save_profile', _('Save Profile'), css_class='btn-primary'),
                css_class='d-grid gap-2'
            )
        )

    class Meta:
        model = ServiceProviderProfile
        fields = [
            'legal_name', 'display_name', 'description', 'phone',
            'contact_name', 'address', 'city', 'state', 'county', 'zip_code',
            'ein', 'website', 'instagram', 'facebook', 'logo', 'is_public',
        ]



    # Validation methods - imported from validation.py
    clean_logo = clean_logo
    clean_phone = clean_phone
    clean_website = clean_website
    clean_instagram = clean_instagram
    clean_facebook = clean_facebook
    clean_ein = clean_ein
    clean_zip_code = clean_zip_code
    clean_legal_name = clean_legal_name
    clean_display_name = clean_display_name
    clean_contact_name = clean_contact_name
    clean_county = clean_county
    clean_address = clean_address
    clean_city = clean_city
    clean_description = clean_description

    def save(self, commit: bool = True) -> ServiceProviderProfile:
        """
        Persist profile updates, processing business logo when needed.

        Returns:
            ServiceProviderProfile: The saved profile instance.
        """
        profile = super().save(commit=False)
        logo = self.cleaned_data.get('logo')

        # Handle logo upload - save directly without complex processing to avoid transaction issues
        if logo and hasattr(logo, 'content_type'):
            # For now, save the image directly to avoid transaction conflicts
            # Image processing can be added later as a separate background task
            profile.logo = logo

        if commit:
            profile.save()

        return profile 