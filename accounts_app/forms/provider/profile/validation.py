"""
Custom Validation Methods

Contains all validation methods for the ServiceProviderProfileForm.
"""

from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _

# --- Local App Imports ---
from ....utils import log_error
from ....utils import normalize_phone

# --- Third-Party Imports (Optional Utils) ---
try:
    from utils.forms import ImageUploadForm
except ImportError:
    ImageUploadForm = None


def clean_logo(self):
    """
    Validate and process the business logo upload with comprehensive checks.

    Returns:
        UploadedFile or None: The validated image or None.

    Raises:
        ValidationError: On invalid, oversized, or unsupported image.
    """
    logo = self.cleaned_data.get('logo')
    if not logo:
        return None

    # Skip validation for existing images (when editing without new upload)
    if hasattr(logo, 'url') and not hasattr(logo, 'content_type'):
        return logo

    # Validate file size (5MB limit)
    if hasattr(logo, 'size') and logo.size > 5 * 1024 * 1024:
        raise ValidationError(
            _('Logo file is too large. Maximum file size is 5MB.'),
            code='file_too_large'
        )

    # Validate file type
    allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif']
    if hasattr(logo, 'content_type'):
        if logo.content_type not in allowed_types:
            raise ValidationError(
                _('Invalid logo format. Please upload a PNG, JPG, WebP, or GIF file.'),
                code='invalid_format'
            )

    # Validate file extension as additional security
    if hasattr(logo, 'name'):
        allowed_extensions = ['.jpg', '.jpeg', '.png', '.webp', '.gif']
        file_extension = '.' + logo.name.split('.')[-1].lower()
        if file_extension not in allowed_extensions:
            raise ValidationError(
                _('Invalid file extension. Please use .jpg, .jpeg, .png, .webp, or .gif files.'),
                code='invalid_extension'
            )

    # Try to open and validate the image using PIL (if available)
    try:
        from PIL import Image
        import io
        
        # Reset file pointer to beginning
        logo.seek(0)
        
        # Try to open the image
        try:
            image = Image.open(logo)
            # Verify it's a valid image
            image.verify()
            
            # Reset file pointer after verification
            logo.seek(0)
            
            # Check image dimensions (minimum size for logo quality)
            image = Image.open(logo)
            width, height = image.size
            
            if width < 50 or height < 50:
                raise ValidationError(
                    _('Logo image is too small. Minimum size is 50x50 pixels.'),
                    code='image_too_small'
                )
            
            if width > 2000 or height > 2000:
                raise ValidationError(
                    _('Logo image is too large. Maximum size is 2000x2000 pixels.'),
                    code='image_too_large'
                )
                
        except Exception as e:
            raise ValidationError(
                _('Invalid image file. Please upload a valid logo image.'),
                code='invalid_image'
            )
        finally:
            # Reset file pointer for saving
            logo.seek(0)
            
    except ImportError:
        # PIL not available, skip advanced image validation
        pass

    # Use ImageUploadForm if available for additional processing
    if ImageUploadForm:
        try:
            form = ImageUploadForm(
                files={'image': logo},
                image_type='logo',
                entity_type='professionals',
                entity_id=self.instance.user.id if self.instance else None
            )
            if not form.is_valid():
                # Extract the first error message
                error_msgs = []
                for field_errors in form.errors.values():
                    error_msgs.extend(field_errors)
                if error_msgs:
                    raise ValidationError(error_msgs[0])
                
        except ValidationError:
            raise
        except Exception as e:
            log_error(
                error_type='image_processing',
                error_message='Failed to process business logo',
                user=self.instance.user if self.instance else None,
                exception=e
            )
            # Don't fail if image processing fails, basic validation passed
            pass

    return logo


def clean_phone(self):
    """
    Validate and normalize phone number format.
    """
    phone = self.cleaned_data.get('phone')
    if not phone:
        return phone
        
    # Use the normalize_phone function for consistent validation
    return normalize_phone(phone)


def clean_website(self):
    """
    Validate website URL format.
    """
    website = self.cleaned_data.get('website')
    if not website:
        return website
        
    import re
    
    # Add protocol if missing
    if not website.startswith(('http://', 'https://')):
        website = 'https://' + website
        
    # Basic URL validation
    url_pattern = re.compile(
        r'^https?://'  # http:// or https://
        r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'  # domain...
        r'localhost|'  # localhost...
        r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # ...or ip
        r'(?::\d+)?'  # optional port
        r'(?:/?|[/?]\S+)$', re.IGNORECASE)
        
    if not url_pattern.match(website):
        raise ValidationError(
            _('Please enter a valid website URL.'),
            code='invalid_website_url'
        )
        
    return website


def clean_instagram(self):
    """
    Validate Instagram URL format.
    """
    instagram = self.cleaned_data.get('instagram')
    if not instagram:
        return instagram
        
    import re
    
    # Allow various Instagram URL formats
    if not instagram.startswith('http'):
        if instagram.startswith('@'):
            instagram = 'https://www.instagram.com/' + instagram[1:]
        elif '/' not in instagram:
            instagram = 'https://www.instagram.com/' + instagram
        else:
            instagram = 'https://' + instagram
            
    # Validate Instagram URL pattern
    instagram_pattern = re.compile(
        r'^https?://(www\.)?instagram\.com/[a-zA-Z0-9._]+/?$',
        re.IGNORECASE
    )
    
    if not instagram_pattern.match(instagram):
        raise ValidationError(
            _('Please enter a valid Instagram profile URL.'),
            code='invalid_instagram_url'
        )
        
    return instagram


def clean_facebook(self):
    """
    Validate Facebook URL format.
    """
    facebook = self.cleaned_data.get('facebook')
    if not facebook:
        return facebook
        
    import re
    
    # Allow various Facebook URL formats
    if not facebook.startswith('http'):
        if '/' not in facebook:
            facebook = 'https://www.facebook.com/' + facebook
        else:
            facebook = 'https://' + facebook
            
    # Validate Facebook URL pattern
    facebook_pattern = re.compile(
        r'^https?://(www\.)?facebook\.com/[a-zA-Z0-9.]+/?$',
        re.IGNORECASE
    )
    
    if not facebook_pattern.match(facebook):
        raise ValidationError(
            _('Please enter a valid Facebook page URL.'),
            code='invalid_facebook_url'
        )
        
    return facebook


def clean_ein(self):
    """
    Validate EIN (Employer Identification Number) format.
    """
    ein = self.cleaned_data.get('ein')
    if not ein:
        return ein
        
    import re
    
    # Remove any formatting
    ein_digits = ''.join(filter(str.isdigit, ein))
    
    # EIN should be exactly 9 digits
    if len(ein_digits) != 9:
        raise ValidationError(
            _('EIN must be exactly 9 digits (format: XX-XXXXXXX).'),
            code='invalid_ein_length'
        )
        
    # Format as XX-XXXXXXX
    formatted_ein = f"{ein_digits[:2]}-{ein_digits[2:]}"
    
    return formatted_ein


def clean_zip_code(self):
    """
    Validate ZIP code format (US).
    """
    zip_code = self.cleaned_data.get('zip_code')
    if not zip_code:
        return zip_code
        
    import re
    
    # Remove any spaces or dashes
    clean_zip = ''.join(filter(str.isdigit, zip_code))
    
    # US ZIP code validation (5 digits or 5+4 digits)
    if len(clean_zip) == 5:
        return clean_zip
    elif len(clean_zip) == 9:
        # Format as XXXXX-XXXX
        return f"{clean_zip[:5]}-{clean_zip[5:]}"
    else:
        raise ValidationError(
            _('Please enter a valid ZIP code (5 digits or 5+4 format).'),
            code='invalid_zip_format'
        )


def clean_legal_name(self):
    """
    Validate legal business name.
    """
    legal_name = self.cleaned_data.get('legal_name')
    if not legal_name:
        return legal_name
        
    # Trim whitespace
    legal_name = legal_name.strip()
    
    # Check minimum length
    if len(legal_name) < 2:
        raise ValidationError(
            _('Business name must be at least 2 characters long.'),
            code='name_too_short'
        )
        
    # Check for basic business name patterns
    import re
    if not re.match(r'^[a-zA-Z0-9\s\-\.\,\&\'\"]+$', legal_name):
        raise ValidationError(
            _('Business name contains invalid characters.'),
            code='invalid_name_chars'
        )
        
    return legal_name


def clean_display_name(self):
    """
    Validate display name (DBA name).
    """
    display_name = self.cleaned_data.get('display_name')
    if not display_name:
        return display_name
        
    # Trim whitespace
    display_name = display_name.strip()
    
    # Check minimum length if provided
    if len(display_name) < 2:
        raise ValidationError(
            _('Display name must be at least 2 characters long.'),
            code='name_too_short'
        )
        
    # Check for valid characters
    import re
    if not re.match(r'^[a-zA-Z0-9\s\-\.\,\&\'\"]+$', display_name):
        raise ValidationError(
            _('Display name contains invalid characters.'),
            code='invalid_name_chars'
        )
        
    return display_name


def clean_contact_name(self):
    """
    Validate contact person name.
    """
    contact_name = self.cleaned_data.get('contact_name')
    if not contact_name:
        return contact_name
        
    # Trim whitespace
    contact_name = contact_name.strip()
    
    # Check minimum length
    if len(contact_name) < 2:
        raise ValidationError(
            _('Contact name must be at least 2 characters long.'),
            code='name_too_short'
        )
    
    # Check maximum length
    if len(contact_name) > 100:
        raise ValidationError(
            _('Contact name must be less than 100 characters.'),
            code='name_too_long'
        )
        
    # Check for valid name characters (letters, spaces, hyphens, apostrophes)
    import re
    if not re.match(r'^[a-zA-Z\s\-\.\'\,]+$', contact_name):
        raise ValidationError(
            _('Contact name contains invalid characters. Use only letters, spaces, hyphens, and apostrophes.'),
            code='invalid_name_chars'
        )
        
    return contact_name


def clean_county(self):
    """
    Validate county name.
    """
    county = self.cleaned_data.get('county')
    if not county:
        return county
        
    # Trim whitespace
    county = county.strip()
    
    # Check minimum length
    if len(county) < 2:
        raise ValidationError(
            _('County name must be at least 2 characters long.'),
            code='name_too_short'
        )
        
    # Check for valid county name characters
    import re
    if not re.match(r'^[a-zA-Z\s\-\.\']+$', county):
        raise ValidationError(
            _('County name contains invalid characters.'),
            code='invalid_county_chars'
        )
        
    return county


def clean_address(self):
    """
    Validate street address.
    """
    address = self.cleaned_data.get('address')
    if not address:
        return address
        
    # Trim whitespace
    address = address.strip()
    
    # Check minimum length
    if len(address) < 5:
        raise ValidationError(
            _('Address must be at least 5 characters long.'),
            code='address_too_short'
        )
        
    # Check for valid address characters
    import re
    if not re.match(r'^[a-zA-Z0-9\s\-\.\,\#\/]+$', address):
        raise ValidationError(
            _('Address contains invalid characters.'),
            code='invalid_address_chars'
        )
        
    return address


def clean_city(self):
    """
    Validate city name.
    """
    city = self.cleaned_data.get('city')
    if not city:
        return city
        
    # Trim whitespace
    city = city.strip()
    
    # Check minimum length
    if len(city) < 2:
        raise ValidationError(
            _('City name must be at least 2 characters long.'),
            code='city_too_short'
        )
        
    # Check for valid city name characters
    import re
    if not re.match(r'^[a-zA-Z\s\-\.\']+$', city):
        raise ValidationError(
            _('City name contains invalid characters.'),
            code='invalid_city_chars'
        )
        
    return city


def clean_description(self):
    """
    Validate business description.
    """
    description = self.cleaned_data.get('description')
    if not description:
        return description
        
    # Trim whitespace
    description = description.strip()
    
    # Check length constraints
    if len(description) > 500:
        raise ValidationError(
            _('Description must be less than 500 characters.'),
            code='description_too_long'
        )
        
    if len(description) < 10:
        raise ValidationError(
            _('Description should be at least 10 characters for meaningful content.'),
            code='description_too_short'
        )
        
    return description 