# --- Django Imports ---
from django import forms
from django.contrib.auth.forms import UserCreationForm
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _

# --- Crispy Forms Imports ---
from crispy_forms.helper import FormHelper
from crispy_forms.layout import Layout, Field, Fieldset, Submit
from crispy_forms.bootstrap import FormActions

# --- Local App Imports ---
from ....models import CustomUser
from ...common import AccessibleFormMixin
from ....mixins import (
    EmailValidationMixin, 
    PasswordValidationMixin, 
    PhoneValidationMixin, 
    BusinessValidationMixin,
)


class ServiceProviderSignupForm(AccessibleFormMixin, EmailValidationMixin, PasswordValidationMixin, PhoneValidationMixin, BusinessValidationMixin, UserCreationForm):
    """
    Sign up a new service provider with business details.

    Features:
    - Email uniqueness validation
    - Password confirmation
    - Business profile defaults
    - Accessible form styling
    """
    email = forms.EmailField(
        label=_('Email Address'),
        max_length=254,
        widget=forms.EmailInput(
            attrs={
                'placeholder': _('Enter your email address'),
                'autocomplete': 'email',
                'required': True,
                'type': 'email',
                'maxlength': '254',
            }
        ),
        help_text=_('This will be your login email. We\'ll send a verification email.')
    )

    password1 = forms.CharField(
        label=_('Password'),
        widget=forms.PasswordInput(
            attrs={
                'placeholder': _('Create a strong password'),
                'autocomplete': 'new-password',
                'required': True,
                'type': 'password',
                'minlength': '12',
                'title': _('Password must be at least 12 characters long.'),
            }
        ),
        help_text=_('Your password must contain at least 12 characters.')
    )

    password2 = forms.CharField(
        label=_('Confirm Password'),
        widget=forms.PasswordInput(
            attrs={
                'placeholder': _('Confirm your password'),
                'autocomplete': 'new-password',
                'required': True,
                'type': 'password',
                'minlength': '12',
            }
        ),
        help_text=_('Enter the same password as before, for verification.')
    )

    business_name = forms.CharField(
        label=_('Business Name'),
        max_length=200,
        required=True,
        widget=forms.TextInput(
            attrs={
                'placeholder': _('Enter your official business name'),
                'required': True,
                'maxlength': '200',
                'minlength': '2',
            }
        ),
        help_text=_('Official registered business name')
    )

    contact_person_name = forms.CharField(
        label=_('Contact Person Name'),
        max_length=100,
        required=True,
        widget=forms.TextInput(
            attrs={
                'placeholder': _('Enter the primary contact person name'),
                'required': True,
                'maxlength': '100',
                'minlength': '2',
            }
        ),
        help_text=_('Name of the primary contact person for the business')
    )

    ein = forms.CharField(
        label=_('EIN (Tax ID)'),
        max_length=20,
        required=False,
        widget=forms.TextInput(
            attrs={
                'placeholder': _('XX-XXXXXXX (optional)'),
            }
        ),
        help_text=_('Employer Identification Number (optional)')
    )

    class Meta:
        model = CustomUser
        fields = ('email', 'password1', 'password2')

    def __init__(self, *args, **kwargs):
        """Initialize form with crispy-forms helper."""
        super().__init__(*args, **kwargs)
        
        # Configure crispy forms helper
        self.helper = FormHelper()
        self.helper.form_method = 'post'
        self.helper.form_class = 'needs-validation'
        
        # Override password help text to prevent HTML list formatting
        self.fields['password1'].help_text = _('Your password must contain at least 12 characters.')
        
        self.helper.layout = Layout(
            Fieldset(
                _('Account Information'),
                Field('email', css_class='form-control'),
                Field('password1', css_class='form-control'),
                Field('password2', css_class='form-control'),
                css_class='mb-4'
            ),
            Fieldset(
                _('Business Information'),
                Field('business_name', css_class='form-control'),
                Field('contact_person_name', css_class='form-control'),
                Field('ein', css_class='form-control'),
                css_class='mb-4'
            ),
            FormActions(
                Submit('signup', _('Create Business Account'), css_class='btn-primary'),
                css_class='d-grid gap-2'
            )
        )

    def clean_email(self) -> str:
        """
        Ensure the email address is unique.

        Raises:
            ValidationError: If the email already exists.

        Returns:
            str: The cleaned email address.
        """
        email = self.cleaned_data.get('email')
        if email and CustomUser.objects.filter(email=email).exists():
            raise ValidationError(
                _('A user with this email already exists.'),
                code='email_exists'
            )
        return email

    def clean_password1(self):
        """
        Validate password1 using Django's password validators.

        This ensures password strength errors are displayed on the password1 field
        instead of password2 field.

        Returns:
            str: The cleaned password.

        Raises:
            ValidationError: If password doesn't meet requirements.
        """
        password1 = self.cleaned_data.get('password1')
        if password1:
            # Import here to avoid circular imports
            from django.contrib.auth.password_validation import validate_password
            try:
                # Create a temporary user instance for validation if none exists
                user = self.instance
                if user is None:
                    # Create a temporary user with the email for validation
                    email = self.cleaned_data.get('email')
                    user = CustomUser(email=email)
                validate_password(password1, user)
            except ValidationError as error:
                raise ValidationError(error.messages)
        return password1

    def clean_password2(self):
        """
        Override the default clean_password2 to avoid duplicate validation errors.

        Only check if passwords match, not password strength (already done in clean_password1).

        Returns:
            str: The cleaned password2.

        Raises:
            ValidationError: If passwords don't match.
        """
        password1 = self.cleaned_data.get('password1')
        password2 = self.cleaned_data.get('password2')

        if password1 and password2 and password1 != password2:
            raise ValidationError(
                _("The two password fields didn't match."),
                code='password_mismatch',
            )
        return password2

    def validate_password_for_user(self, user):
        """
        Override to prevent password validation errors from being added to password2.

        Since we already validate passwords in clean_password1, we don't need
        to validate them again here. This prevents duplicate validation and
        ensures errors appear on the password1 field.
        """
        # Do nothing - password validation is handled in clean_password1
        # This prevents Django from running validation again and adding errors to password2
        pass

    def save(self, commit: bool = True) -> CustomUser:
        """
        Create and return a new service provider user.

        Args:
            commit (bool): Whether to save the user immediately.

        Returns:
            CustomUser: The created user instance.
        """
        user = super().save(commit=False)
        user.email = self.cleaned_data['email']
        user.role = CustomUser.SERVICE_PROVIDER
        user.is_active = True  # Active but requires email verification
        if commit:
            user.save()
        return user 