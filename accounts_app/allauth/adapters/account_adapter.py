"""
Custom account adapter for role-based user creation and management.

This adapter extends DefaultAccountAdapter to provide:
- Role-based user registration with automatic profile creation
- Custom validation for role-specific signup requirements
- Integration with existing business logic and security features
- Enhanced email verification workflow
- Phone number normalization and validation
"""

import logging
from typing import Dict, Any, Optional

from django.conf import settings
from django.core.exceptions import ValidationError
from django.db import transaction
from django.http import HttpRequest
from django.utils.translation import gettext_lazy as _
from django.urls import reverse

from allauth.account.adapter import DefaultAccountAdapter
from allauth.account.models import EmailAddress
from allauth.exceptions import ImmediateHttpResponse

# Local imports
from ...constants import UserRoles, UserStatus, DefaultValues
from ...models import (
    CustomUser,
    UserPreferences, UserSecurity, EmailPreferences,
    ProfilePrivacySettings
)
from ...models.customer import CustomerProfile
from ...models.provider import ServiceProviderProfile
from ...services import CustomerRegistrationService, ProviderRegistrationService
from ...utils.common.validators import normalize_phone
from ...utils import log_account_lifecycle_event, log_authentication_event


logger = logging.getLogger(__name__)


class CozyWishAccountAdapter(DefaultAccountAdapter):
    """
    Custom account adapter for role-based user creation and management.
    
    This adapter extends DefaultAccountAdapter to provide:
    - Role-based user registration with automatic profile creation
    - Custom validation for role-specific signup requirements
    - Integration with existing business logic and security features
    - Enhanced email verification workflow
    - Phone number normalization and validation
    """
    
    def __init__(self, request=None):
        super().__init__(request)
        self.request = request
        
        # Ensure request has session for allauth compatibility
        if request and not hasattr(request, 'session'):
            logger.warning(f"Request missing session attribute, creating empty session: {request}")
            # Create a minimal session-like object to prevent errors
            class DummySession:
                def __init__(self):
                    self._data = {}
                def get(self, key, default=None):
                    return self._data.get(key, default)
                def set(self, key, value):
                    self._data[key] = value
            request.session = DummySession()
    
    def save_user(self, request: HttpRequest, user: CustomUser, form, commit: bool = True) -> CustomUser:
        """
        Save user with role-based profile creation and business logic integration.
        
        This method handles:
        - Role assignment based on signup context
        - Automatic profile creation (CustomerProfile or ServiceProviderProfile)
        - Security record creation (UserSecurity, UserPreferences, EmailPreferences)
        - Phone number normalization
        - Business logic integration with existing services
        
        Args:
            request: HTTP request object
            user: CustomUser instance to save
            form: The signup form with user data
            commit: Whether to save the user to database
            
        Returns:
            CustomUser: The saved user instance with complete profile setup
        """
        # Ensure request has session for allauth compatibility
        if request and not hasattr(request, 'session'):
            logger.warning(f"Request missing session in save_user, creating empty session: {request}")
            # Create a minimal session-like object to prevent errors
            class DummySession:
                def __init__(self):
                    self._data = {}
                def get(self, key, default=None):
                    return self._data.get(key, default)
                def set(self, key, value):
                    self._data[key] = value
            request.session = DummySession()
            
        try:
            with transaction.atomic():
                # Extract role from form or request context
                role = self._determine_user_role(request, form)
                
                # Set basic user fields
                user.role = role
                
                # Handle email verification based on role
                if role == UserRoles.CUSTOMER:
                    # Customers don't need email verification - activate immediately
                    user.status = UserStatus.ACTIVE
                    user.email_verified = True
                    user.is_active = True
                else:
                    # Service providers need email verification
                    user.status = UserStatus.PENDING_VERIFICATION
                    user.email_verified = False
                    user.is_active = True  # Activated but requires email verification

                # Populate credentials from the signup form so model validation passes
                if hasattr(form, 'cleaned_data'):
                    user.email = form.cleaned_data.get('email', '')
                    raw_password = form.cleaned_data.get('password1') or form.cleaned_data.get('password')
                    if raw_password:
                        user.set_password(raw_password)
                
                # Extract and normalize additional data from form
                additional_data = self._extract_form_data(form)
                
                # Validate role-specific requirements
                self._validate_role_requirements(role, additional_data)
                
                # Save the user first
                if commit:
                    user.save()
                    
                    # Create role-specific profile and related records
                    self._create_user_profile(user, role, additional_data, request)
                    self._create_security_records(user)
                    self._create_preference_records(user)
                    
                    # Log account creation
                    log_account_lifecycle_event(
                        event_type='registration',
                        user=user,
                        request=None,
                        reason='email_signup'
                    )
                    
                    logger.info(f"User account created successfully: {user.email} (role: {role})")
                
                return user
                
        except Exception as e:
            logger.error(f"Error saving user during registration: {str(e)}", exc_info=True)
            raise ValidationError(_("Registration failed. Please try again."))
    
    def add_email(self, request: HttpRequest, user: CustomUser, email: str) -> None:
        """
        Override add_email to immediately verify customer email addresses.
        
        Args:
            request: HTTP request object
            user: User object
            email: Email address being added
        """
        # Call parent method to create EmailAddress
        super().add_email(request, user, email)
        
        # For customers, immediately mark email as verified
        if user.role == UserRoles.CUSTOMER:
            try:
                from allauth.account.models import EmailAddress
                email_address = EmailAddress.objects.get(user=user, email=email)
                if not email_address.verified:
                    email_address.verified = True
                    email_address.primary = True
                    email_address.save()
                    logger.info(f"Customer EmailAddress immediately verified: {email}")
            except EmailAddress.DoesNotExist:
                logger.warning(f"EmailAddress not found for customer: {email}")
            except Exception as e:
                logger.error(f"Error verifying customer EmailAddress: {str(e)}")
    
    def _determine_user_role(self, request: HttpRequest, form) -> str:
        """
        Determine user role based on form data and request context.
        
        Priority:
        1. URL path analysis (e.g., /provider/signup/) - highest priority
        2. Form field 'role' if explicitly provided and different from default
        3. Request parameter 'role'
        4. Default to customer
        
        Args:
            request: HTTP request object
            form: The signup form
            
        Returns:
            str: User role (customer, service_provider, or admin)
        """
        # Analyze URL path for role context (highest priority for auto-determination)
        path = request.path.lower()
        if 'provider' in path or 'business' in path:
            return UserRoles.SERVICE_PROVIDER
        elif 'customer' in path:
            return UserRoles.CUSTOMER
        elif 'admin' in path:
            return UserRoles.ADMIN
        
        # Check form data if URL doesn't specify role and form has explicit non-default value
        if hasattr(form, 'cleaned_data') and 'role' in form.cleaned_data:
            role = form.cleaned_data['role']
            # Only use form role if it's explicitly set to something other than default
            if role and role != UserRoles.CUSTOMER and role in [UserRoles.SERVICE_PROVIDER, UserRoles.ADMIN]:
                return role
        
        # Check request parameters as fallback
        role = request.GET.get('role') or request.POST.get('role')
        if role in [UserRoles.CUSTOMER, UserRoles.SERVICE_PROVIDER, UserRoles.ADMIN]:
            return role
        
        # Default to customer
        return UserRoles.CUSTOMER
    
    def _extract_form_data(self, form) -> Dict[str, Any]:
        """
        Extract additional data from the signup form.
        
        Args:
            form: The signup form
            
        Returns:
            Dict containing extracted form data
        """
        data = {}
        
        if not hasattr(form, 'cleaned_data'):
            return data
        
        # Common fields
        fields_to_extract = [
            'first_name', 'last_name', 'phone_number', 'gender',
            'birth_month', 'birth_year', 'address', 'city', 'state',
            'zip_code', 'business_name', 'contact_person_name', 'ein',
            'website', 'description'
        ]
        
        for field in fields_to_extract:
            if field in form.cleaned_data:
                value = form.cleaned_data[field]
                if value:  # Only include non-empty values
                    data[field] = value
        
        # Normalize phone number if present
        if 'phone_number' in data and data['phone_number']:
            try:
                data['phone_number'] = normalize_phone(data['phone_number'])
            except ValidationError:
                # Remove invalid phone number
                del data['phone_number']
        
        return data
    
    def _validate_role_requirements(self, role: str, data: Dict[str, Any]) -> None:
        """
        Validate role-specific requirements.
        
        Args:
            role: User role
            data: Form data
            
        Raises:
            ValidationError: If requirements are not met
        """
        if role == UserRoles.SERVICE_PROVIDER:
            # Service providers need business information
            required_fields = ['business_name', 'contact_person_name']
            missing_fields = [field for field in required_fields if not data.get(field)]
            
            if missing_fields:
                raise ValidationError(
                    _("Service providers must provide business information: {}").format(
                        ', '.join(missing_fields)
                    )
                )
    
    def _create_user_profile(self, user: CustomUser, role: str, data: Dict[str, Any], request: HttpRequest) -> None:
        """
        Create role-specific user profile.
        
        Args:
            user: CustomUser instance
            role: User role
            data: Form data
            request: HTTP request object
        """
        if role == UserRoles.CUSTOMER:
            CustomerProfile.objects.create(
                user=user,
                first_name=data.get('first_name', ''),
                last_name=data.get('last_name', ''),
                phone_number=data.get('phone_number', ''),
                gender=data.get('gender', ''),
                birth_month=data.get('birth_month'),
                birth_year=data.get('birth_year'),
                address=data.get('address', ''),
                city=data.get('city', ''),
                zip_code=data.get('zip_code', '')
            )
            
        elif role == UserRoles.SERVICE_PROVIDER:
            ServiceProviderProfile.objects.create(
                user=user,
                legal_name=data.get('business_name', ''),
                contact_name=data.get('contact_person_name', ''),
                phone=data.get('phone_number', ''),
                address=data.get('address', ''),
                city=data.get('city', ''),
                state=data.get('state', ''),
                zip_code=data.get('zip_code', ''),
                ein=data.get('ein', ''),
                website=data.get('website', ''),
                description=data.get('description', '')
            )
    
    def _create_security_records(self, user: CustomUser) -> None:
        """Create security records for user."""
        UserSecurity.objects.create(user=user)
    
    def _create_preference_records(self, user: CustomUser) -> None:
        """Create preference records for user."""
        # Create user preferences
        UserPreferences.objects.create(user=user)
        
        # Create email preferences
        EmailPreferences.objects.create(
            user=user,
            email_notifications_enabled=True,
            account_notifications=True,
            booking_notifications=True,
            payment_notifications=True,
            marketing_emails=False,  # Conservative default
            event_notifications=user.is_customer,
            review_notifications=True
        )
        
        # Create privacy settings
        privacy_settings = ProfilePrivacySettings.get_default_settings()
        ProfilePrivacySettings.objects.create(user=user, **privacy_settings)
    
    def clean_email(self, email: str) -> str:
        """
        Clean and validate email address.
        
        Args:
            email: Email address to clean
            
        Returns:
            str: Cleaned email address
        """
        email = super().clean_email(email)
        
        # Additional validation for CozyWish
        if not email or '@' not in email:
            raise ValidationError(_("Please enter a valid email address."))
        
        # Check for disposable email domains if enabled
        if hasattr(settings, 'BLOCK_DISPOSABLE_EMAILS') and settings.BLOCK_DISPOSABLE_EMAILS:
            domain = email.split('@')[1].lower()
            if domain in getattr(settings, 'DISPOSABLE_EMAIL_DOMAINS', []):
                raise ValidationError(_("Disposable email addresses are not allowed."))
        
        return email.lower()
    
    def is_open_for_signup(self, request: HttpRequest) -> bool:
        """
        Check if signup is open.
        
        Args:
            request: HTTP request object
            
        Returns:
            bool: True if signup is open
        """
        # Check global settings
        if hasattr(settings, 'ACCOUNT_ALLOW_REGISTRATION'):
            if not settings.ACCOUNT_ALLOW_REGISTRATION:
                return False
        
        # Check if user is already authenticated
        if request.user.is_authenticated:
            return False
        
        # Additional business logic checks
        try:
            # Check if registration service allows signup
            # Use appropriate registration service based on user role
            if user.role == UserRoles.CUSTOMER:
                registration_service = CustomerRegistrationService()
            else:
                registration_service = ProviderRegistrationService()
            return registration_service.is_registration_open(request)
        except Exception as e:
            logger.error(f"Error checking registration status: {str(e)}")
            return True  # Default to allowing signup
    
    def is_email_verification_required(self, request: HttpRequest, user: CustomUser = None) -> bool:
        """
        Check if email verification is required.
        
        Args:
            request: HTTP request object
            user: User object (optional)
            
        Returns:
            bool: True if email verification is required
        """
        # Customers don't need email verification
        if user and user.role == UserRoles.CUSTOMER:
            return False
        
        # Service providers need email verification
        if user and user.role == UserRoles.SERVICE_PROVIDER:
            return True
        
        # Check global settings
        return getattr(settings, 'ACCOUNT_EMAIL_VERIFICATION', 'mandatory') != 'none'
    
    def send_confirmation_mail(self, request: HttpRequest, emailconfirmation, signup: bool) -> None:
        """
        Send confirmation email with custom template.
        
        Args:
            request: HTTP request object
            emailconfirmation: EmailConfirmation instance
            signup: Whether this is for signup
        """
        # Use custom email template for service providers
        if emailconfirmation.email_address.user.role == UserRoles.SERVICE_PROVIDER:
            # Custom template for service provider verification
            template_name = 'emails/service_provider_verification.html'
        else:
            # Default template for customers
            template_name = 'emails/email_verification.html'
        
        # Send email with custom template
        try:
            from django.core.mail import send_mail
            from django.template.loader import render_to_string
            
            context = {
                'user': emailconfirmation.email_address.user,
                'confirmation_url': request.build_absolute_uri(
                    reverse('account_confirm_email', args=[emailconfirmation.key])
                ),
                'site_name': getattr(settings, 'SITE_NAME', 'CozyWish')
            }
            
            message = render_to_string(template_name, context)
            
            send_mail(
                subject=_('Verify your email address'),
                message=message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[emailconfirmation.email_address.email],
                html_message=message
            )
            
            logger.info(f"Verification email sent to: {emailconfirmation.email_address.email}")
            
        except Exception as e:
            logger.error(f"Error sending verification email: {str(e)}")
            # Fall back to parent method
            super().send_confirmation_mail(request, emailconfirmation, signup)
    
    def confirm_email(self, request: HttpRequest, email_address: EmailAddress) -> None:
        """
        Handle email confirmation with role-specific logic.
        
        Args:
            request: HTTP request object
            email_address: EmailAddress instance
        """
        # Call parent method first
        super().confirm_email(request, email_address)
        
        # Update user status for service providers
        user = email_address.user
        if user.role == UserRoles.SERVICE_PROVIDER:
            user.status = UserStatus.ACTIVE
            user.email_verified = True
            user.save()
            
            logger.info(f"Service provider email confirmed and account activated: {user.email}")
            
            # Log the confirmation event
            log_account_lifecycle_event(
                event_type='email_confirmation',
                user=user,
                request=request,
                reason='service_provider_verification'
            )
    
    def is_login_by_code_required(self, login):
        """
        Check if login by code is required.

        Args:
            login: Login instance

        Returns:
            bool: True if login by code is required
        """
        # For CozyWish, we don't require login by code for customers
        # This simplifies the authentication flow
        if hasattr(login, 'user') and login.user and hasattr(login.user, 'role'):
            if login.user.role == UserRoles.CUSTOMER:
                return False

        # For other cases, use default behavior but ensure we have a request
        try:
            return super().is_login_by_code_required(login)
        except AttributeError:
            # If there's an issue with request/session, default to False
            logger.warning("Error in is_login_by_code_required, defaulting to False")
            return False
    
    def get_logout_redirect_url(self, request: HttpRequest) -> str:
        """
        Get logout redirect URL.
        
        Args:
            request: HTTP request object
            
        Returns:
            str: Redirect URL
        """
        # Role-specific logout redirects
        if request.user.is_authenticated:
            if request.user.role == UserRoles.SERVICE_PROVIDER:
                # Redirect providers to home page (dashboard URLs are currently disabled)
                return reverse('home_app:home')
            elif request.user.role == UserRoles.CUSTOMER:
                # Redirect customers to home page
                return reverse('home_app:home')
            elif request.user.role == UserRoles.ADMIN:
                # Redirect admins to home page
                return reverse('home_app:home')
        
        # Default to home page
        return reverse('home_app:home')
    
    def is_open_for_login(self, request: HttpRequest, email: str) -> bool:
        """
        Check if login is open for the given email.
        
        Args:
            request: HTTP request object
            email: Email address
            
        Returns:
            bool: True if login is open
        """
        try:
            user = CustomUser.objects.get(email=email)
            
            # Check if account is locked
            if user.status == UserStatus.LOCKED:
                logger.warning(f"Login attempt for locked account: {email}")
                return False
            
            # Check if account is suspended
            if user.status == UserStatus.SUSPENDED:
                logger.warning(f"Login attempt for suspended account: {email}")
                return False
            
            # Check if account is pending verification (for service providers)
            if user.status == UserStatus.PENDING_VERIFICATION and user.role == UserRoles.SERVICE_PROVIDER:
                logger.info(f"Service provider login attempt before email verification: {email}")
                # Allow login but show verification message
                return True
            
            return True
            
        except CustomUser.DoesNotExist:
            # Allow login attempt for non-existent users (will fail gracefully)
            return True
        except Exception as e:
            logger.error(f"Error checking login status for {email}: {str(e)}")
            return True  # Default to allowing login
    
    def pre_login(self, request: HttpRequest, user: CustomUser, **kwargs) -> None:
        """
        Perform actions before login.
        
        Args:
            request: HTTP request object
            user: User object
            **kwargs: Additional arguments
        """
        # Log login attempt
        log_authentication_event(
            event_type='login_attempt',
            user_email=user.email,
            success=True,
            request=request,
            additional_data={'method': 'email_password'}
        )
        
        # Check if user needs to complete profile
        if user.role == UserRoles.SERVICE_PROVIDER:
            if not hasattr(user, 'service_provider_profile'):
                logger.info(f"Service provider without profile attempting login: {user.email}")
        
        # Call parent method
        super().pre_login(request, user, **kwargs)
    
    def new_user(self, request: HttpRequest) -> CustomUser:
        """
        Create new user instance.
        
        Args:
            request: HTTP request object
            
        Returns:
            CustomUser: New user instance
        """
        user = super().new_user(request)
        
        # Set default role based on URL context
        role = self._determine_user_role(request, None)
        user.role = role
        
        # For customers, set email_verified to True from the start
        if role == UserRoles.CUSTOMER:
            user.email_verified = True
            user.status = UserStatus.ACTIVE
            logger.info(f"New customer user - email verification disabled: {user.email}")
        
        return user 