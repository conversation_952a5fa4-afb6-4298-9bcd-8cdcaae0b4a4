# --- Standard Library Imports ---
from typing import Optional
from datetime import datetime

# --- Django Imports ---
from django.core.exceptions import ValidationError
from django.core.validators import RegexValidator
from django.db import models
from django.urls import reverse
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

# --- Local App Imports ---
from .auth import CustomUser
from ..constants import Gender, ModelConstants
from ..utils import (
    get_customer_profile_image_path,
    validate_image_file
)


class UserProfile(models.Model):
    """
    Stores basic user profile information.
    Separated from CustomUser to maintain single responsibility.
    """
    user = models.OneToOneField(
        CustomUser,
        on_delete=models.CASCADE,
        related_name='user_profile',
        verbose_name=_('user account')
    )
    first_name = models.CharField(
        _('first name'),
        max_length=ModelConstants.NAME_MAX_LENGTH,
        blank=True
    )
    last_name = models.CharField(
        _('last name'),
        max_length=ModelConstants.NAME_MAX_LENGTH,
        blank=True
    )
    profile_picture = models.ImageField(
        _('profile picture'),
        upload_to=get_customer_profile_image_path,
        blank=True,
        null=True
    )
    phone_number = models.CharField(
        _('phone number'),
        max_length=ModelConstants.PHONE_MAX_LENGTH,
        blank=True,
        validators=[RegexValidator(
            regex=r'^\+?1?\d{9,15}$',
            message=_('Enter a valid phone number (e.g., +**********)')
        )]
    )
    birth_date = models.DateField(
        _('birth date'),
        null=True,
        blank=True
    )
    gender = models.CharField(
        _('gender'),
        max_length=1,
        choices=Gender.CHOICES,
        blank=True
    )
    bio = models.TextField(
        _('bio'),
        max_length=ModelConstants.DESCRIPTION_MAX_LENGTH,
        blank=True
    )
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)

    class Meta:
        verbose_name = _('User Profile')
        verbose_name_plural = _('User Profiles')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user'], name='user_profile_user_idx'),
            models.Index(fields=['created_at'], name='user_profile_created_idx'),
        ]

    def __str__(self):
        return f"{self.user.email} - Profile"

    @property
    def full_name(self) -> str:
        """Get the full name of the user"""
        if self.first_name and self.last_name:
            return f"{self.first_name} {self.last_name}"
        return self.first_name or self.last_name or ""

    @property
    def age(self) -> Optional[int]:
        """Calculate age from birth date"""
        if self.birth_date:
            today = timezone.now().date()
            return today.year - self.birth_date.year - ((today.month, today.day) < (self.birth_date.month, self.birth_date.day))
        return None

    def clean(self):
        """Validate profile data"""
        super().clean()
        
        # Validate image file if provided
        if self.profile_picture:
            validate_image_file(self.profile_picture)
            
        # Validate phone number if provided
        if self.phone_number:
            self.user.validate_phone(self.phone_number)


 