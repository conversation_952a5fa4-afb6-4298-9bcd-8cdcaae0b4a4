# --- Standard Library Imports ---
from datetime import datetime

# --- Django Imports ---
from django.core.validators import RegexValidator
from django.db import models
from django.urls import reverse
from django.utils.translation import gettext_lazy as _

# --- Local App Imports ---
from .auth import CustomUser
from ..constants import Gender, Months, ModelConstants
from ..utils import (
    get_customer_profile_image_path,
    validate_image_file, process_profile_image, create_thumbnail
)


class CustomerProfile(models.Model):
    """Stores comprehensive customer profile information"""
    
    user = models.OneToOneField(
        CustomUser,
        on_delete=models.CASCADE,
        related_name='customer_profile',
        verbose_name=_('user account')
    )
    first_name = models.CharField(
        _('first name'), 
        max_length=100,
        blank=True
    )
    last_name = models.CharField(
        _('last name'), 
        max_length=100,
        blank=True
    )
    profile_picture = models.ImageField(
        _('profile picture'),
        upload_to=get_customer_profile_image_path,
        blank=True,
        null=True,
        help_text=_('Profile image uploaded to cloud storage (max 5MB, JPEG/PNG/GIF/WEBP)')
    )
    gender = models.CharField(
        _('gender'),
        max_length=1,
        choices=Gender.CHOICES,
        blank=True
    )
    birth_month = models.PositiveSmallIntegerField(
        _('birth month'),
        choices=Months.CHOICES,
        null=True,
        blank=True
    )
    birth_year = models.PositiveSmallIntegerField(
        _('birth year'),
        null=True,
        blank=True,
        help_text=_('Format: YYYY')
    )
    phone_number = models.CharField(
        _('phone number'),
        max_length=20,
        blank=True,
        validators=[RegexValidator(
            regex=r'^\+?1?\d{9,15}$',
            message=_('Enter a valid phone number (e.g., +1234567890)')
        )]
    )
    address = models.CharField(
        _('street address'),
        max_length=255,
        blank=True
    )
    city = models.CharField(
        _('city'),
        max_length=100,
        blank=True
    )
    zip_code = models.CharField(
        _('postal code'),
        max_length=10,
        blank=True
    )
    created_at = models.DateTimeField(_('created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('updated at'), auto_now=True)

    class Meta:
        verbose_name = _('Customer Profile')
        verbose_name_plural = _('Customer Profiles')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user'], name='customer_profile_user_idx'),
            models.Index(fields=['created_at'], name='customer_profile_created_idx'),
        ]

    def __str__(self):
        return f"{self.user.email} - Customer Profile"

    @property
    def full_name(self) -> str:
        """Get the full name of the customer"""
        if self.first_name and self.last_name:
            return f"{self.first_name} {self.last_name}"
        return self.first_name or self.last_name or ""

    @property
    def birth_month_name(self) -> str:
        """Get the name of the birth month"""
        if self.birth_month:
            return dict(Months.CHOICES)[self.birth_month]
        return ""

    def get_absolute_url(self):
        return reverse('accounts_app:customer_profile_detail', kwargs={'pk': self.pk})

    def clean(self):
        """Validate customer profile data"""
        super().clean()
        
        # Validate image file if provided
        if self.profile_picture:
            validate_image_file(self.profile_picture)

    def save(self, *args, **kwargs):
        """Custom save method to handle image processing"""
        # Process profile picture if provided
        if self.profile_picture:
            self.profile_picture = process_profile_image(self.profile_picture)
            
        super().save(*args, **kwargs)
        
        # Create thumbnail after saving
        if self.profile_picture:
            create_thumbnail(self.profile_picture) 