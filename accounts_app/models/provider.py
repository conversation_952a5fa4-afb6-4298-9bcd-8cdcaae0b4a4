# --- Standard Library Imports ---
from datetime import datetime

# --- Django Imports ---
from django.core.validators import RegexValidator
from django.db import models
from django.urls import reverse
from django.utils.translation import gettext_lazy as _

# --- Local App Imports ---
from .auth import CustomUser
from ..constants import USStates
from ..utils import (
    get_provider_profile_image_path,
    validate_image_file
)


class ServiceProviderProfile(models.Model):
    """Represents a service provider business with complete contact and operational details"""

    user = models.OneToOneField(
        CustomUser,
        on_delete=models.CASCADE,
        related_name='service_provider_profile',
        verbose_name=_('business account')
    )
    legal_name = models.CharField(
        _('legal business name'),
        max_length=200,
        help_text=_('Official registered business name')
    )
    display_name = models.CharField(
        _('public display name'),
        max_length=200,
        blank=True,
        help_text=_('Name shown to customers (if different from legal name)')
    )
    description = models.TextField(
        _('business description'),
        max_length=500,
        blank=True,
        help_text=_('Brief overview of services offered (500 characters max)')
    )
    logo = models.ImageField(
        _('business logo'),
        upload_to=get_provider_profile_image_path,
        blank=True,
        null=True,
        help_text=_('Company logo displayed on your profile (max 5MB, JPEG/PNG/GIF/WEBP)')
    )
    phone = models.CharField(
        _('business phone'),
        max_length=20,
        blank=True,
        help_text=_('Business phone number (can be added later)'),
        validators=[RegexValidator(
            regex=r'^\+?1?\d{9,15}$',
            message=_('Enter a valid phone number (e.g., +**********)')
        )]
    )
    contact_name = models.CharField(
        _('primary contact'),
        max_length=100,
        help_text=_('Name of main business contact')
    )
    address = models.CharField(
        _('street address'),
        max_length=255,
        blank=True,
        help_text=_('Business address (can be added later)')
    )
    city = models.CharField(
        _('city'),
        max_length=100,
        blank=True,
        help_text=_('City (can be added later)')
    )
    state = models.CharField(
        _('state'),
        max_length=2,
        choices=USStates.CHOICES,
        blank=True,
        help_text=_('State (can be added later)')
    )
    county = models.CharField(
        _('county'),
        max_length=100,
        blank=True
    )
    zip_code = models.CharField(
        _('ZIP code'),
        max_length=10,
        blank=True,
        help_text=_('ZIP code (can be added later)')
    )
    ein = models.CharField(
        _('EIN number'),
        max_length=20,
        blank=True,
        help_text=_('Employer Identification Number (optional)')
    )
    website = models.URLField(
        _('website URL'),
        blank=True
    )
    instagram = models.URLField(
        _('Instagram URL'),
        blank=True
    )
    facebook = models.URLField(
        _('Facebook URL'),
        blank=True
    )
    is_public = models.BooleanField(
        _('public visibility'),
        default=True,
        help_text=_('Show business in public listings')
    )
    
    # Tutorial and onboarding tracking
    venue_creation_tutorial_completed = models.BooleanField(
        _('venue creation tutorial completed'),
        default=False,
        help_text=_('Whether the user has completed the venue creation guided tour')
    )
    
    created = models.DateTimeField(_('created at'), auto_now_add=True)
    updated = models.DateTimeField(_('updated at'), auto_now=True)

    class Meta:
        verbose_name = _('Service Provider')
        verbose_name_plural = _('Service Providers')
        ordering = ['-created']
        indexes = [
            models.Index(fields=['user'], name='provider_profile_user_idx'),
            models.Index(fields=['created'], name='provider_profile_created_idx'),
            models.Index(fields=['state'], name='provider_profile_state_idx'),
            models.Index(fields=['is_public'], name='provider_profile_public_idx'),
        ]

    def __str__(self):
        return f"{self.business_name} - Service Provider"

    @property
    def business_name(self) -> str:
        """Get the business name (display name or legal name)"""
        return self.display_name or self.legal_name

    @property
    def full_address(self) -> str:
        """Get the full formatted address"""
        address_parts = [
            self.address,
            self.city,
            f"{self.state} {self.zip_code}".strip()
        ]
        return ", ".join(filter(None, address_parts))

    def get_absolute_url(self):
        return reverse('accounts_app:provider_profile_detail', kwargs={'pk': self.pk})

    def clean(self):
        """Validate service provider profile data"""
        super().clean()
        
        # Validate image file if provided
        if self.logo:
            validate_image_file(self.logo) 