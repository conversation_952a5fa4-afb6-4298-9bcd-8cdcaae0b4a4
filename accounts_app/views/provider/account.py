# --- Third-Party Imports ---
from django.contrib import messages
from django.shortcuts import redirect, render
from django.views.decorators.http import require_http_methods
from django.utils.translation import gettext_lazy as _
import logging

# --- Local App Imports ---
from ...decorators import service_provider_required
from ...forms import AccountDeactivationForm
from ...utils import log_error
from ...services import AuthenticationService, AccountService
from ..common import MESSAGES

# Provider-specific logger
provider_logger = logging.getLogger('accounts_app.provider')


# --- Service Provider Account Management ---

@service_provider_required
@require_http_methods(["GET", "POST"])
def service_provider_deactivate_account_view(request):
    """
    Deactivate service provider account using AccountService.
    """
    try:
        if request.method == 'POST':
            form = AccountDeactivationForm(user=request.user, data=request.POST)
            
            if form.is_valid():
                reason = form.cleaned_data.get('reason', 'Service provider requested deactivation')
                
                # Use service to deactivate account
                success, message = AccountService.deactivate_account(
                    user=request.user,
                    reason=reason,
                    request=request
                )
                
                if success:
                    # Logout user
                    AuthenticationService.logout_user(request)
                    messages.success(request, MESSAGES['account_deactivated'])
                    provider_logger.info(f"Service provider account deactivated: {request.user.email}")
                    return redirect('home')
                else:
                    messages.error(request, message)
                    provider_logger.error(f"Service provider account deactivation failed: {request.user.email}")
            else:
                # Log form validation errors
                if form.errors:
                    log_error(
                        error_type='form_validation',
                        error_message="Service provider account deactivation form validation failed",
                        user=request.user,
                        request=request,
                        details={'form_errors': form.errors}
                    )
                    provider_logger.warning(f"Service provider account deactivation form validation failed: {form.errors}")
        else:
            form = AccountDeactivationForm(user=request.user)
        
        return render(request, 'accounts_app/provider/deactivate_account.html', {'form': form})
        
    except Exception as e:
        log_error(
            error_type='account_deactivation',
            error_message="Unexpected error during service provider account deactivation",
            user=request.user,
            request=request,
            exception=e
        )
        messages.error(request, MESSAGES['deactivation_error'])
        provider_logger.error(f"Service provider account deactivation error: {str(e)}", exc_info=True)
        return render(request, 'accounts_app/provider/deactivate_account.html', {'form': AccountDeactivationForm(user=request.user)}) 