# --- Third-Party Imports ---
from django.conf import settings
from django.contrib import messages
from django.contrib.auth.views import (
    PasswordResetCompleteView,
    PasswordResetConfirmView,
    PasswordResetDoneView,
    PasswordResetView,
)
from django.core.exceptions import ValidationError
from django.db import transaction
from django.http import HttpResponseRedirect
from django.shortcuts import redirect, render
from django.urls import reverse, reverse_lazy
from django.utils.decorators import method_decorator
from django.views.decorators.http import require_http_methods
from django.views.generic import CreateView, DetailView, FormView, UpdateView
from django.utils.translation import gettext_lazy as _
import logging

# --- Local App Imports ---
from ...decorators import ServiceProviderRequiredMixin, AnonymousRequiredMixin, service_provider_required
from ...forms import (
    ServiceProviderSignupForm,
)
from ...utils import (
    log_account_lifecycle_event,
    log_authentication_event,
    log_error,
    log_user_activity,
    performance_monitor,
)
from ...models import CustomUser, ServiceProviderProfile
from ...services import (
    AuthenticationService,
    ProviderRegistrationService,
    ProviderProfileService,
    PasswordService,
    AccountService,
    EmailVerificationService,
    TeamService,
    AuthenticationError,
    ProfileError,
    SecurityError,
)
from ..common import MESSAGES, logger, record_login_attempt, get_client_ip

# Provider-specific logger
provider_logger = logging.getLogger('accounts_app.provider')


# --- Service Provider Authentication ---

class ServiceProviderSignupView(AnonymousRequiredMixin, CreateView):
    """
    Service provider registration with email verification workflow using ProviderRegistrationService.
    """
    model = CustomUser
    form_class = ServiceProviderSignupForm
    template_name = 'accounts_app/provider/signup.html'
    success_url = reverse_lazy('accounts_app:provider_signup_done')
    
    def form_valid(self, form):
        """Process valid signup form submissions using ProviderRegistrationService"""
        try:
            # Prepare user data for service
            user_data = {
                'email': form.cleaned_data['email'],
                'password': form.cleaned_data['password1'],
                'first_name': form.cleaned_data.get('first_name', ''),
                'last_name': form.cleaned_data.get('last_name', ''),
                'business_name': form.cleaned_data['business_name'],
                'contact_person_name': form.cleaned_data['contact_person_name'],
                'ein': form.cleaned_data.get('ein', ''),
            }
            
            # Use service to register provider
            success, user, message = ProviderRegistrationService.register_service_provider(
                user_data=user_data,
                request=self.request
            )
            
            if success and user:
                self.object = user
                messages.success(self.request, MESSAGES['provider_signup'])
                provider_logger.info(f"Service provider registration successful: {user.email}")
                return HttpResponseRedirect(self.get_success_url())
            else:
                messages.error(self.request, message or _("Registration failed. Please try again."))
                provider_logger.error(f"Service provider registration failed: {message}")
                return self.form_invalid(form)
                
        except Exception as error:
            log_error(
                error_type='provider_signup',
                error_message="Unexpected error during service provider signup",
                request=self.request,
                exception=error,
                details={'form_data': form.cleaned_data}
            )
            messages.error(self.request, _("Registration failed. Please try again."))
            provider_logger.error(f"Service provider signup error: {str(error)}", exc_info=True)
            return self.form_invalid(form)

    def form_invalid(self, form):
        """Handle invalid form submissions"""
        # Log form validation errors
        if form.errors:
            log_error(
                error_type='form_validation',
                error_message="Service provider signup form validation failed",
                request=self.request,
                details={'form_errors': form.errors}
            )
            provider_logger.warning(f"Service provider signup form validation failed: {form.errors}")
        
        return super().form_invalid(form)


@require_http_methods(["GET"])
@performance_monitor("provider_signup_done")
def provider_signup_done_view(request):
    """
    Display signup completion page after successful registration with error handling.
    """
    try:
        return render(request, 'accounts_app/provider/signup_done.html')
    except Exception as e:
        log_error(
            error_type='template_render',
            error_message="Error rendering provider signup done page",
            request=request,
            exception=e
        )
        provider_logger.error(f"Provider signup done page error: {str(e)}", exc_info=True)
        return render(request, 'accounts_app/provider/signup_done.html')


@require_http_methods(["GET"])
@performance_monitor("provider_email_verification")
def provider_email_verify_view(request, uidb64, token):
    """
    Handle email verification for service provider accounts using AccountService.
    """
    try:
        from django.contrib.auth.tokens import default_token_generator
        from django.utils.http import urlsafe_base64_decode
        from django.utils.encoding import force_str
        
        # Decode user ID
        try:
            uid = force_str(urlsafe_base64_decode(uidb64))
            user = CustomUser.objects.get(pk=uid)
        except (TypeError, ValueError, OverflowError, CustomUser.DoesNotExist):
            user = None
        
        if user is not None and default_token_generator.check_token(user, token):
            # Use service to verify email
            success, message = AccountService.verify_email(user, request)
            
            if success:
                messages.success(request, MESSAGES['email_verified'])
                provider_logger.info(f"Service provider email verified: {user.email}")
                return redirect('accounts_app:provider_login')
            else:
                messages.error(request, message)
                provider_logger.error(f"Service provider email verification failed: {user.email}")
                return render(request, 'accounts_app/provider/email_verification_failed.html')
        else:
            messages.error(request, MESSAGES['verification_link_invalid'])
            provider_logger.warning(f"Invalid verification link accessed: {uidb64}")
            return render(request, 'accounts_app/provider/email_verification_failed.html')
            
    except Exception as e:
        log_error(
            error_type='email_verification',
            error_message="Unexpected error during provider email verification",
            request=request,
            exception=e,
            details={'uidb64': uidb64, 'token': token}
        )
        messages.error(request, _("Verification failed. Please try again."))
        provider_logger.error(f"Provider email verification error: {str(e)}", exc_info=True)
        return render(request, 'accounts_app/provider/email_verification_failed.html') 