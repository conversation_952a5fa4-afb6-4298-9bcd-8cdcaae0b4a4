# --- Standard Library Imports ---
import logging

# --- Third-Party Imports ---
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.shortcuts import get_object_or_404, redirect, render
from django.views.decorators.http import require_http_methods
from django.utils.translation import gettext_lazy as _

# --- Local App Imports ---
from .common import logger
from ..decorators import service_provider_required
from ..forms import TeamMemberForm
from ..utils import log_error, log_team_management_event, performance_monitor
from ..models import ServiceProviderProfile, TeamMember
from ..services import TeamService, ProfileError, SecurityError

# Team-specific logger
team_logger = logging.getLogger('accounts_app.team')



# --- Team Management Views ---

@service_provider_required
@require_http_methods(["GET"])
def service_provider_team_list_view(request):
    """
    Display team members list with comprehensive error handling.
    """
    try:
        # Get service provider profile
        profile = ServiceProviderProfile.objects.get(user=request.user)
        team_members = TeamMember.objects.filter(service_provider=profile).order_by('name')
        
        context = {
            'team_members': team_members,
            'profile': profile,
            'max_team_members': TeamMember.max_count(),
            'current_count': team_members.count(),
        }
        
        team_logger.info(f"Team list accessed by: {request.user.email}")
        return render(request, 'accounts_app/provider/team_list.html', context)
        
    except ServiceProviderProfile.DoesNotExist:
        log_error(
            error_type='profile_not_found',
            error_message="Service provider profile not found for team list",
            user=request.user,
            request=request
        )
        messages.error(request, _("Profile not found. Please contact support."))
        team_logger.error(f"Service provider profile not found for team list: {request.user.email}")
        return redirect('accounts_app:service_provider_profile')
    except Exception as e:
        log_error(
            error_type='team_list',
            error_message="Error displaying team members list",
            user=request.user,
            request=request,
            exception=e
        )
        messages.error(request, _("Error loading team members. Please try again."))
        team_logger.error(f"Team list error: {str(e)}", exc_info=True)
        return redirect('accounts_app:service_provider_profile')


@service_provider_required
@require_http_methods(["GET", "POST"])
def service_provider_team_add_view(request):
    """
    Add team member using TeamService with comprehensive error handling.
    """
    try:
        # Get service provider profile
        profile = ServiceProviderProfile.objects.get(user=request.user)
        
        if request.method == 'POST':
            form = TeamMemberForm(data=request.POST, files=request.FILES)
            
            if form.is_valid():
                member_data = {
                    'name': form.cleaned_data['name'],
                    'position': form.cleaned_data['position'],
                    'photo': form.cleaned_data.get('photo'),
                    'is_active': form.cleaned_data.get('is_active', True),
                }
                
                # Use service to add team member
                success, team_member, message = TeamService.add_team_member(
                    service_provider=profile,
                    member_data=member_data,
                    request=request
                )
                
                if success:
                    messages.success(request, _("Team member added successfully"))
                    team_logger.info(f"Team member added: {member_data['name']} to {profile.legal_name}")
                    return redirect('accounts_app:service_provider_team_list')
                else:
                    messages.error(request, message)
                    team_logger.error(f"Team member addition failed: {message}")
            else:
                # Log form validation errors
                if form.errors:
                    log_error(
                        error_type='form_validation',
                        error_message="Team member form validation failed",
                        user=request.user,
                        request=request,
                        details={'form_errors': form.errors}
                    )
                    team_logger.warning(f"Team member form validation failed: {form.errors}")
        else:
            form = TeamMemberForm()
        
        context = {
            'form': form,
            'profile': profile,
            'max_team_members': TeamMember.max_count(),
            'current_count': TeamMember.objects.filter(service_provider=profile).count(),
        }
        
        return render(request, 'accounts_app/provider/team_member_form.html', context)
        
    except ServiceProviderProfile.DoesNotExist:
        log_error(
            error_type='profile_not_found',
            error_message="Service provider profile not found for team member addition",
            user=request.user,
            request=request
        )
        messages.error(request, _("Profile not found. Please contact support."))
        team_logger.error(f"Service provider profile not found for team member addition: {request.user.email}")
        return redirect('accounts_app:service_provider_profile')
    except Exception as e:
        log_error(
            error_type='team_member_add',
            error_message="Unexpected error during team member addition",
            user=request.user,
            request=request,
            exception=e
        )
        messages.error(request, _("Error adding team member. Please try again."))
        team_logger.error(f"Team member addition error: {str(e)}", exc_info=True)
        return redirect('accounts_app:service_provider_team_list')


@service_provider_required
@require_http_methods(["GET", "POST"])
def service_provider_team_edit_view(request, team_member_id):
    """
    Edit team member using TeamService with comprehensive error handling.
    """
    try:
        # Get service provider profile and team member
        profile = ServiceProviderProfile.objects.get(user=request.user)
        team_member = TeamMember.objects.get(id=team_member_id, service_provider=profile)
        
        if request.method == 'POST':
            form = TeamMemberForm(data=request.POST, files=request.FILES, instance=team_member)
            
            if form.is_valid():
                member_data = {
                    'name': form.cleaned_data['name'],
                    'position': form.cleaned_data['position'],
                    'photo': form.cleaned_data.get('photo'),
                    'is_active': form.cleaned_data.get('is_active', True),
                }
                
                # Use service to update team member
                success, message = TeamService.update_team_member(
                    team_member=team_member,
                    member_data=member_data,
                    request=request
                )
                
                if success:
                    messages.success(request, _("Team member updated successfully"))
                    team_logger.info(f"Team member updated: {team_member.name}")
                    return redirect('accounts_app:service_provider_team_list')
                else:
                    messages.error(request, message)
                    team_logger.error(f"Team member update failed: {message}")
            else:
                # Log form validation errors
                if form.errors:
                    log_error(
                        error_type='form_validation',
                        error_message="Team member edit form validation failed",
                        user=request.user,
                        request=request,
                        details={'form_errors': form.errors}
                    )
                    team_logger.warning(f"Team member edit form validation failed: {form.errors}")
        else:
            form = TeamMemberForm(instance=team_member)
        
        context = {
            'form': form,
            'team_member': team_member,
            'profile': profile,
        }
        
        return render(request, 'accounts_app/provider/team_member_form.html', context)
        
    except (ServiceProviderProfile.DoesNotExist, TeamMember.DoesNotExist):
        messages.error(request, _("Team member not found"))
        team_logger.error(f"Team member not found: {team_member_id}")
        return redirect('accounts_app:service_provider_team_list')
    except Exception as e:
        log_error(
            error_type='team_member_edit',
            error_message="Unexpected error during team member edit",
            user=request.user,
            request=request,
            exception=e,
            details={'team_member_id': team_member_id}
        )
        messages.error(request, _("Error editing team member. Please try again."))
        team_logger.error(f"Team member edit error: {str(e)}", exc_info=True)
        return redirect('accounts_app:service_provider_team_list')


@service_provider_required
@require_http_methods(["POST"])
def service_provider_team_delete_view(request, team_member_id):
    """
    Delete team member using TeamService with comprehensive error handling.
    """
    try:
        # Get service provider profile and team member
        profile = ServiceProviderProfile.objects.get(user=request.user)
        team_member = TeamMember.objects.get(id=team_member_id, service_provider=profile)
        
        # Use service to remove team member
        success, message = TeamService.remove_team_member(
            team_member=team_member,
            request=request
        )
        
        if success:
            messages.success(request, _("Team member removed successfully"))
            team_logger.info(f"Team member removed: {team_member.name}")
        else:
            messages.error(request, message)
            team_logger.error(f"Team member removal failed: {message}")
        
        return redirect('accounts_app:service_provider_team_list')
        
    except (ServiceProviderProfile.DoesNotExist, TeamMember.DoesNotExist):
        messages.error(request, _("Team member not found"))
        team_logger.error(f"Team member not found for deletion: {team_member_id}")
        return redirect('accounts_app:service_provider_team_list')
    except Exception as e:
        log_error(
            error_type='team_member_delete',
            error_message="Unexpected error during team member deletion",
            user=request.user,
            request=request,
            exception=e,
            details={'team_member_id': team_member_id}
        )
        messages.error(request, _("Error removing team member. Please try again."))
        team_logger.error(f"Team member deletion error: {str(e)}", exc_info=True)
        return redirect('accounts_app:service_provider_team_list')


@service_provider_required
@require_http_methods(["GET"])
def service_provider_team_detail_view(request, team_member_id):
    """
    Display team member details with comprehensive error handling.
    """
    try:
        # Get service provider profile and team member
        profile = ServiceProviderProfile.objects.get(user=request.user)
        team_member = TeamMember.objects.get(id=team_member_id, service_provider=profile)
        
        context = {
            'team_member': team_member,
            'profile': profile,
        }
        
        team_logger.info(f"Team member detail accessed: {team_member.name}")
        return render(request, 'accounts_app/provider/team_member_detail.html', context)
        
    except (ServiceProviderProfile.DoesNotExist, TeamMember.DoesNotExist):
        messages.error(request, _("Team member not found"))
        team_logger.error(f"Team member not found for detail: {team_member_id}")
        return redirect('accounts_app:service_provider_team_list')
    except Exception as e:
        log_error(
            error_type='team_member_detail',
            error_message="Unexpected error during team member detail view",
            user=request.user,
            request=request,
            exception=e,
            details={'team_member_id': team_member_id}
        )
        messages.error(request, _("Error loading team member details. Please try again."))
        team_logger.error(f"Team member detail error: {str(e)}", exc_info=True)
        return redirect('accounts_app:service_provider_team_list')


@service_provider_required
@require_http_methods(["POST"])
def service_provider_team_toggle_status_view(request, team_member_id):
    """
    Toggle team member active status using TeamService with comprehensive error handling.
    """
    try:
        # Get service provider profile and team member
        profile = ServiceProviderProfile.objects.get(user=request.user)
        team_member = TeamMember.objects.get(id=team_member_id, service_provider=profile)
        
        # Toggle active status
        new_status = not team_member.is_active
        member_data = {
            'is_active': new_status,
        }
        
        # Use service to update team member
        success, message = TeamService.update_team_member(
            team_member=team_member,
            member_data=member_data,
            request=request
        )
        
        if success:
            status_text = "activated" if new_status else "deactivated"
            messages.success(request, _(f"Team member {status_text} successfully"))
            team_logger.info(f"Team member {status_text}: {team_member.name}")
        else:
            messages.error(request, message)
            team_logger.error(f"Team member status toggle failed: {message}")
        
        return redirect('accounts_app:service_provider_team_list')
        
    except (ServiceProviderProfile.DoesNotExist, TeamMember.DoesNotExist):
        messages.error(request, _("Team member not found"))
        team_logger.error(f"Team member not found for status toggle: {team_member_id}")
        return redirect('accounts_app:service_provider_team_list')
    except Exception as e:
        log_error(
            error_type='team_member_toggle_status',
            error_message="Unexpected error during team member status toggle",
            user=request.user,
            request=request,
            exception=e,
            details={'team_member_id': team_member_id}
        )
        messages.error(request, _("Error updating team member status. Please try again."))
        team_logger.error(f"Team member status toggle error: {str(e)}", exc_info=True)
        return redirect('accounts_app:service_provider_team_list')

