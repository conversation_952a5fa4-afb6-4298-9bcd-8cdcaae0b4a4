# --- Third-Party Imports ---
from django.contrib import messages
from django.http import HttpResponseRedirect
from django.shortcuts import redirect, render
from django.urls import reverse_lazy
from django.utils.translation import gettext_lazy as _
from django.views.generic import FormView
import logging

# --- Local App Imports ---
from ...decorators import CustomerRequiredMixin
from ...forms import AccountDeactivationForm
from ...services import AuthenticationService, AccountService
from ...utils import log_error
from ..common import MESSAGES

# Customer-specific logger
customer_logger = logging.getLogger('accounts_app.customer')


# --- Customer Account Management Views ---

class CustomerDeactivateAccountView(CustomerRequiredMixin, FormView):
    """
    Handle customer account deactivation with security safeguards using AccountService.
    """
    form_class = AccountDeactivationForm
    template_name = 'accounts_app/customer/deactivate_account.html'
    success_url = reverse_lazy('home')

    def get_form_kwargs(self):
        """Pass user to form"""
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user
        return kwargs

    def form_valid(self, form):
        """Handle valid account deactivation using AccountService"""
        try:
            reason = form.cleaned_data.get('reason', 'User requested deactivation')
            
            # Use service to deactivate account
            success, message = AccountService.deactivate_account(
                user=self.request.user,
                reason=reason,
                request=self.request
            )
            
            if success:
                # Logout user
                AuthenticationService.logout_user(self.request)
                messages.success(self.request, MESSAGES['account_deactivated'])
                customer_logger.info(f"Customer account deactivated: {self.request.user.email}")
                return HttpResponseRedirect(self.get_success_url())
            else:
                messages.error(self.request, message)
                customer_logger.error(f"Customer account deactivation failed: {self.request.user.email}")
                return self.form_invalid(form)
                
        except Exception as e:
            log_error(
                error_type='account_deactivation',
                error_message="Unexpected error during customer account deactivation",
                user=self.request.user,
                request=self.request,
                exception=e
            )
            messages.error(self.request, MESSAGES['deactivation_error'])
            customer_logger.error(f"Customer account deactivation error: {str(e)}", exc_info=True)
            return self.form_invalid(form)

    def form_invalid(self, form):
        """Handle invalid form submissions"""
        # Log form validation errors
        if form.errors:
            log_error(
                error_type='form_validation',
                error_message="Customer account deactivation form validation failed",
                user=self.request.user,
                request=self.request,
                details={'form_errors': form.errors}
            )
            customer_logger.warning(f"Customer account deactivation form validation failed: {form.errors}")
        
        return super().form_invalid(form) 