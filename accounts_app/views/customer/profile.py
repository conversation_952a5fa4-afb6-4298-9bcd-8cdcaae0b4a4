# --- Third-Party Imports ---
from django.contrib import messages
from django.http import HttpResponseRedirect
from django.shortcuts import redirect, render
from django.urls import reverse_lazy
from django.utils.translation import gettext_lazy as _
from django.views.generic import DetailView, UpdateView
import logging

# --- Local App Imports ---
from ...decorators import CustomerRequiredMixin
from ...forms import CustomerProfileForm
from ...models.customer import CustomerProfile
from ...services import CustomerProfileService, ProfileError
from ...utils import log_error, log_profile_change
from ..common import MESSAGES

# Customer-specific logger
customer_logger = logging.getLogger('accounts_app.customer')


# --- Customer Profile Management Views ---

class CustomerProfileView(CustomerRequiredMixin, DetailView):
    """
    Display authenticated customer's profile details with error handling.
    """
    model = CustomerProfile
    template_name = 'accounts_app/customer/profile.html'
    context_object_name = 'profile'

    def get_object(self, queryset=None):
        """Get or create customer profile with error handling"""
        try:
            profile, created = CustomerProfile.objects.get_or_create(user=self.request.user)
            if created:
                customer_logger.info(f"Customer profile created for: {self.request.user.email}")
            return profile
        except Exception as e:
            log_error(
                error_type='profile_retrieval',
                error_message="Error retrieving customer profile",
                user=self.request.user,
                request=self.request,
                exception=e
            )
            customer_logger.error(f"Customer profile retrieval error: {str(e)}", exc_info=True)
            # Return a basic profile object to prevent template errors
            return CustomerProfile(user=self.request.user)


class CustomerProfileEditView(CustomerRequiredMixin, UpdateView):
    """
    Handle customer profile updates with validation using CustomerProfileService.
    """
    model = CustomerProfile
    form_class = CustomerProfileForm
    template_name = 'accounts_app/customer/profile_edit.html'
    success_url = reverse_lazy('accounts_app:customer_profile')

    def get_object(self, queryset=None):
        """Get or create customer profile with error handling"""
        try:
            profile, created = CustomerProfile.objects.get_or_create(user=self.request.user)
            if created:
                customer_logger.info(f"Customer profile created for: {self.request.user.email}")
            return profile
        except Exception as e:
            log_error(
                error_type='profile_retrieval',
                error_message="Error retrieving customer profile for edit",
                user=self.request.user,
                request=self.request,
                exception=e
            )
            customer_logger.error(f"Customer profile edit retrieval error: {str(e)}", exc_info=True)
            # Return a basic profile object
            return CustomerProfile(user=self.request.user)

    def post(self, request, *args, **kwargs):
        """Handle profile update with comprehensive error handling"""
        try:
            self.object = self.get_object()
            
            # Handle profile picture only updates
            if request.FILES.get('profile_picture') and not any(
                request.POST.get(field) for field in ['first_name', 'last_name', 'phone_number']
            ):
                return self._handle_profile_picture_only_update(request)
            
            # Handle regular form submission
            form = self.get_form()
            if form.is_valid():
                return self.form_valid(form)
            else:
                return self.form_invalid(form)
                
        except Exception as e:
            log_error(
                error_type='profile_update',
                error_message="Unexpected error during customer profile update",
                user=request.user,
                request=request,
                exception=e
            )
            messages.error(request, _("An unexpected error occurred. Please try again."))
            customer_logger.error(f"Customer profile update error: {str(e)}", exc_info=True)
            return self.form_invalid(self.get_form())

    def _handle_profile_picture_only_update(self, request):
        """Handle profile picture only updates using CustomerProfileService"""
        try:
            profile_data = {
                'profile_picture': request.FILES.get('profile_picture')
            }
            
            # Use service to update profile
            success, message = CustomerProfileService.update_customer_profile(
                user=request.user,
                profile_data=profile_data,
                request=request
            )
            
            if success:
                messages.success(request, MESSAGES['profile_update'])
                customer_logger.info(f"Customer profile picture updated: {request.user.email}")
                return redirect(self.success_url)
            else:
                messages.error(request, message)
                customer_logger.error(f"Customer profile picture update failed: {request.user.email}")
                return self.form_invalid(self.get_form())
                
        except Exception as e:
            log_error(
                error_type='profile_picture_update',
                error_message="Error updating customer profile picture",
                user=request.user,
                request=request,
                exception=e
            )
            messages.error(request, _("Failed to update profile picture. Please try again."))
            customer_logger.error(f"Customer profile picture update error: {str(e)}", exc_info=True)
            return self.form_invalid(self.get_form())

    def form_valid(self, form):
        """Handle valid form submission using CustomerProfileService"""
        try:
            # Prepare profile data for service
            profile_data = form.cleaned_data
            
            # Use service to update profile
            success, message = CustomerProfileService.update_customer_profile(
                user=self.request.user,
                profile_data=profile_data,
                request=self.request
            )
            
            if success:
                messages.success(self.request, MESSAGES['profile_update'])
                customer_logger.info(f"Customer profile updated successfully: {self.request.user.email}")
                return HttpResponseRedirect(self.get_success_url())
            else:
                messages.error(self.request, message)
                customer_logger.error(f"Customer profile update failed: {self.request.user.email}")
                return self.form_invalid(form)
                
        except ProfileError as e:
            messages.error(self.request, str(e))
            customer_logger.error(f"Customer profile update error: {str(e)}")
            return self.form_invalid(form)
        except Exception as e:
            log_error(
                error_type='profile_update',
                error_message="Unexpected error during customer profile update",
                user=self.request.user,
                request=self.request,
                exception=e
            )
            messages.error(self.request, _("An unexpected error occurred. Please try again."))
            customer_logger.error(f"Customer profile update error: {str(e)}", exc_info=True)
            return self.form_invalid(form)

    def form_invalid(self, form):
        """Handle invalid form submissions"""
        # Log form validation errors
        if form.errors:
            log_error(
                error_type='form_validation',
                error_message="Customer profile form validation failed",
                user=self.request.user,
                request=self.request,
                details={'form_errors': form.errors}
            )
            customer_logger.warning(f"Customer profile form validation failed: {form.errors}")
        
        return super().form_invalid(form) 