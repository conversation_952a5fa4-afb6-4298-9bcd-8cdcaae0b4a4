from django.urls import path
# Local views
from . import views
from .views import ajax, privacy, allauth_views

# Import Business landing page view from home_app so that the historic
# URL namespace ``accounts_app:for_business`` continues to work after
# moving the page into home_app.
from home_app.views import for_business_view
from allauth.account import views as allauth_account_views
from allauth.account import views as allauth_views  # Added for legacy route aliases


app_name = 'accounts_app'

urlpatterns = [
    # ------------------------------------------------------------------
    # Service-Provider onboarding / “For Business” landing page
    # ------------------------------------------------------------------
    # Legacy alias retained for backward compatibility with templates and
    # third-party links.  It simply delegates to ``home_app.for_business_view``.
    path('for-business/', for_business_view, name='for_business'),

    # ---------------------------------------------------------------------
    # Legacy Authentication Aliases
    # ---------------------------------------------------------------------
    # These routes **wrap** the corresponding django-allauth views so that
    # the historic URL names used throughout the code-base and test-suite
    # continue to resolve after migrating to allauth.  They simply delegate
    # to allauth and therefore remain a thin compatibility layer.
    # Customer aliases
    path('customer/login/', views.allauth_views.CozyWishLoginView.as_view(), name='customer_login'),
    path('customer/logout/', views.allauth_views.CozyWishLogoutView.as_view(), name='customer_logout'),
    path('customer/register/', allauth_views.SignupView.as_view(), name='customer_register'),
    path('customer/signup/', views.allauth_views.CozyWishSignupView.as_view(), name='customer_signup'),

    # Service-provider aliases (now using custom views for complete business signup)
    path('provider/login/', views.allauth_views.CozyWishProviderLoginView.as_view(), name='provider_login'),
    path('service-provider/login/', views.allauth_views.CozyWishProviderLoginView.as_view(), name='service_provider_login'),
    path('provider/register/', views.ServiceProviderSignupView.as_view(), name='provider_register'),
    path('provider/signup/', views.ServiceProviderSignupView.as_view(), name='service_provider_signup'),
    path('provider/signup/done/', views.provider_signup_done_view, name='provider_signup_done'),

    # Generic Logout (alias to allauth's logout view) so templates can use
    # ``{% url 'accounts_app:logout' %}`` consistently regardless of user role.
    path('logout/', views.allauth_views.CozyWishLogoutView.as_view(), name='logout'),

    # Customer Profile URLs (keeping custom views for profile management)
    path('customer/profile/', views.CustomerProfileView.as_view(), name='customer_profile'),
    path('customer/profile/edit/', views.CustomerProfileEditView.as_view(), name='customer_profile_edit'),
    path('customer/change-password/', views.CustomerPasswordChangeView.as_view(), name='customer_change_password'),
    path('customer/deactivate/', views.CustomerDeactivateAccountView.as_view(), name='customer_deactivate'),
    
    # Service Provider Profile URLs (keeping custom views for profile management)
    path('provider/profile/', views.ServiceProviderProfileView.as_view(), name='service_provider_profile'),
    path('provider/profile/edit/', views.ServiceProviderProfileEditView.as_view(), name='service_provider_profile_edit'),
    path('provider/deactivate/', views.service_provider_deactivate_account_view, name='service_provider_deactivate'),
    
    # AJAX Endpoints
    path('ajax/customer/profile/update/', ajax.CustomerProfileAjaxView.as_view(), name='customer_profile_ajax'),
    path('ajax/provider/profile/update/', ajax.ServiceProviderProfileAjaxView.as_view(), name='provider_profile_ajax'),
    path('ajax/validate-field/', ajax.validate_field_ajax, name='validate_field_ajax'),
    
    # Email Verification URLs
    path('verify-email/<str:token>/', views.email_verification_view, name='email_verification'),
    path('resend-verification/', views.resend_verification_email_view, name='resend_verification'),
    path('email-verification-success/', views.email_verification_success_view, name='email_verification_success'),
    path('email-verification-sent/', views.email_verification_sent_view, name='email_verification_sent'),
    
    # Privacy Settings URLs
    path('privacy/settings/', privacy.PrivacySettingsView.as_view(), name='privacy_settings'),
    path('privacy/settings/update/', privacy.PrivacySettingsUpdateView.as_view(), name='privacy_settings_update'),
    path('privacy/quick-toggle/', privacy.privacy_settings_quick_toggle, name='privacy_quick_toggle'),
    
    # GDPR & Data Export URLs
    path('data/export/', privacy.DataExportView.as_view(), name='data_export'),
    path('data/export/download/<str:filename>/', privacy.DataExportDownloadView.as_view(), name='data_export_download'),
    path('data/deletion/request/', privacy.DataDeletionRequestView.as_view(), name='data_deletion_request'),
    
    # Profile Verification URLs
    path('verification/status/', privacy.VerificationBadgeView.as_view(), name='verification_status'),
    path('verification/request/', privacy.RequestVerificationView.as_view(), name='request_verification'),
    path('verification/status/ajax/', privacy.VerificationStatusView.as_view(), name='verification_status_ajax'),
    path('verification/summary/', privacy.user_verification_summary, name='verification_summary'),
    
    # Note: Authentication URLs (signup, login, logout, password reset) are now handled by allauth
    # These are available via the allauth.urls include in project_root/urls.py
    # Common allauth URLs will be available at:
    # - /accounts/login/
    # - /accounts/logout/
    # - /accounts/signup/
    # - /accounts/password/reset/
    # - /accounts/password/reset/done/
    # - /accounts/password/reset/key/<key>/
    # - /accounts/password/reset/key/done/
    # - /accounts/confirm-email/
    # - /accounts/confirm-email/<key>/
]
