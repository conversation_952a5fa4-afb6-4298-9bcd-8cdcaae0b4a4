"""
Email services for accounts_app.

This module contains the EmailService and EmailVerificationService classes
for handling email operations.
"""

import logging
from typing import Dict, Tuple, Any, Optional, List
from django.conf import settings
from django.contrib.auth.tokens import default_token_generator
from django.core.mail import send_mail, EmailMultiAlternatives
from django.template.loader import render_to_string
from django.utils.encoding import force_bytes
from django.utils.html import strip_tags
from django.utils.http import urlsafe_base64_encode
from django.utils.translation import gettext_lazy as _

# Local imports
from ...models import CustomUser, EmailVerificationToken


logger = logging.getLogger(__name__)


class EmailVerificationService:
    """
    Service for handling email verification operations.
    
    Provides methods for sending verification emails and managing
    email verification tokens.
    """
    
    @staticmethod
    def send_verification_email(user: CustomUser, request=None) -> Tuple[bool, str]:
        """
        Send email verification email to user.
        
        Args:
            user: User object
            request: HTTP request object for URL building
            
        Returns:
            Tuple of (success, message)
        """
        try:
            # Generate verification token
            token = default_token_generator.make_token(user)
            uid = urlsafe_base64_encode(force_bytes(user.pk))
            
            # Build verification URL
            if request:
                verification_url = request.build_absolute_uri(
                    f'/accounts/verify-email/{uid}/{token}/'
                )
            else:
                # Use Django's configured site domain with fallback
                protocol = getattr(settings, 'ACCOUNT_DEFAULT_HTTP_PROTOCOL', 'https')
                domain = getattr(settings, 'ALLOWED_HOSTS', ['localhost'])[0] if hasattr(settings, 'ALLOWED_HOSTS') and settings.ALLOWED_HOSTS else 'localhost'
                verification_url = f"{protocol}://{domain}/accounts/verify-email/{uid}/{token}/"
            
            # Email content
            subject = 'CozyWish - Verify Your Email Address'
            message = f"""
            Hello {user.get_full_name() or 'User'},
            
            Thank you for signing up with CozyWish!
            
            Please click the link below to verify your email address:
            {verification_url}
            
            This link will expire in 24 hours for security reasons.
            
            Best regards,
            The CozyWish Team
            """
            
            # Send email
            send_mail(
                subject=subject,
                message=message.strip(),
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[user.email],
                fail_silently=False,
            )
            
            logger.info(f"Verification email sent to: {user.email}")
            return True, _("Verification email sent successfully")
            
        except Exception as e:
            logger.error(f"Email verification send error: {str(e)}", exc_info=True)
            return False, _("Failed to send verification email. Please try again.")


class EmailService:
    """
    Service for sending templated emails with preference checking.
    """

    @staticmethod
    def send_templated_email(
        subject: str,
        template_name: str,
        context: Dict[str, Any],
        to_email: str,
        email_type: str = 'general',
        from_email: str = None,
        cc: Optional[List[str]] = None,
        bcc: Optional[List[str]] = None,
        reply_to: Optional[str] = None,
        request=None,
    ) -> Tuple[bool, str]:
        """
        Send an email using a template with preference checking.

        Args:
            subject: Email subject.
            template_name: Name of the template to render.
            context: Dictionary of context variables for the template.
            to_email: Recipient email address.
            email_type: Type of email for preference checking.
            from_email: Sender email address.
            cc: List of CC email addresses (optional).
            bcc: List of BCC email addresses (optional).
            reply_to: Reply-to email address (optional).
            request: HTTP request object for logging (optional).

        Returns:
            Tuple of (success, message).
        """
        try:
            # Set default from_email if not provided
            if from_email is None:
                from_email = settings.DEFAULT_FROM_EMAIL

            # Check user email preferences
            if not EmailService._can_send_email(to_email, email_type):
                return False, "User has opted out of this email type"

            # Render template
            html_message = render_to_string(template_name, context)
            text_message = strip_tags(html_message)

            # Create email message
            msg = EmailMultiAlternatives(
                subject=subject,
                body=text_message,
                from_email=from_email,
                to=[to_email],
                cc=cc,
                bcc=bcc,
                reply_to=[reply_to] if reply_to else None,
            )
            msg.attach_alternative(html_message, "text/html")

            # Send email
            msg.send()
            logger.info(f"Templated email sent to {to_email}, subject: {subject}")
            return True, "Email sent successfully"
        except Exception as e:
            logger.error(f"Templated email send error: {str(e)}", exc_info=True)
            return False, "Failed to send email. Please try again."

    @staticmethod
    def _can_send_email(to_email: str, email_type: str) -> bool:
        """
        Check if user can receive the specified email type.
        
        Args:
            to_email: Recipient email address
            email_type: Type of email to check
            
        Returns:
            bool: True if email can be sent, False otherwise
        """
        try:
            user = CustomUser.objects.get(email=to_email)
            email_prefs = getattr(user, 'email_preferences', None)
            
            if email_prefs:
                return email_prefs.can_send_email(email_type)
            
            # Default to True if no preferences set
            return True
        except CustomUser.DoesNotExist:
            return True
        except Exception:
            return True

    @staticmethod
    def send_verification_email(user: CustomUser, request=None) -> Tuple[bool, str]:
        """
        Send email verification email using the new token system.
        
        Args:
            user: User object
            request: HTTP request object for URL building
            
        Returns:
            Tuple of (success, message)
        """
        try:
            # Revoke any existing tokens for this user
            EmailVerificationToken.revoke_user_tokens(user)
            
            # Generate new verification token
            token = EmailVerificationToken.generate_token(user, request=request)
            
            # Build verification URL
            if request:
                verification_url = request.build_absolute_uri(
                    f'/accounts/verify-email/{token.token}/'
                )
            else:
                # Use Django's configured site domain with fallback
                protocol = getattr(settings, 'ACCOUNT_DEFAULT_HTTP_PROTOCOL', 'https')
                domain = getattr(settings, 'ALLOWED_HOSTS', ['localhost'])[0] if hasattr(settings, 'ALLOWED_HOSTS') and settings.ALLOWED_HOSTS else 'localhost'
                verification_url = f"{protocol}://{domain}/accounts/verify-email/{token.token}/"
            
            # Prepare email context
            context = {
                'user': user,
                'verification_url': verification_url,
                'token': token.token,
                'site_name': 'CozyWish',
                'expires_at': token.expires_at,
            }
            
            # Send email
            return EmailService.send_templated_email(
                subject='CozyWish - Verify Your Email Address',
                template_name='emails/email_verification.html',
                context=context,
                to_email=user.email,
                email_type='account',
                request=request
            )
            
        except Exception as e:
            logger.error(f"Email verification send error: {str(e)}", exc_info=True)
            return False, "Failed to send verification email. Please try again." 