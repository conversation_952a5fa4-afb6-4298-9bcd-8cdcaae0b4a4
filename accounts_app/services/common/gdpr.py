"""
GDPR service for accounts_app.

This module contains the GDPRService class for handling
GDPR compliance operations.
"""

import json
import csv
import os
import logging
import xml.etree.ElementTree as ET
from datetime import datetime, timedelta
from typing import Dict, Tuple, Any, Optional
from django.conf import settings
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

# Local imports
from ...models import CustomUser
from ...utils import log_user_activity


logger = logging.getLogger(__name__)


class GDPRService:
    """
    Service for handling GDPR compliance operations.
    
    Provides methods for data export, data deletion, and privacy management
    to comply with GDPR requirements.
    """
    
    @staticmethod
    def export_user_data(user: CustomUser, request=None) -> Tuple[bool, Optional[Dict[str, Any]], str]:
        """
        Export all user data for GDPR compliance.
        
        Args:
            user: User object
            request: HTTP request object for logging
            
        Returns:
            Tuple of (success, data_dict, message)
        """
        try:
            # Record the data export request
            log_user_activity(
                activity_type='data_export_requested',
                user=user,
                request=request,
                details={'export_type': 'full_data_export'}
            )
            
            # Collect all user data
            user_data = {
                'export_info': {
                    'exported_at': timezone.now().isoformat(),
                    'exported_by': user.email,
                    'export_type': 'GDPR Data Export',
                    'format_version': '1.0'
                },
                'user_account': GDPRService._get_user_account_data(user),
                'profile_data': GDPRService._get_profile_data(user),
                'preferences': GDPRService._get_preferences_data(user),
                'security_data': GDPRService._get_security_data(user),
                'activity_history': GDPRService._get_activity_history(user),
                'verification_data': GDPRService._get_verification_data(user),
                'privacy_settings': GDPRService._get_privacy_settings(user),
                'business_data': GDPRService._get_business_data(user),
                'email_preferences': GDPRService._get_email_preferences(user),
            }
            
            # Remove empty sections
            user_data = {k: v for k, v in user_data.items() if v}
            
            logger.info(f"User data exported successfully for: {user.email}")
            return True, user_data, _("Data export completed successfully")
            
        except Exception as e:
            logger.error(f"Data export error for user {user.email}: {str(e)}", exc_info=True)
            return False, None, _("Failed to export data. Please try again.")
    
    @staticmethod
    def _get_user_account_data(user: CustomUser) -> Dict[str, Any]:
        """Get basic user account data"""
        return {
            'email': user.email,
            'role': user.get_role_display(),
            'status': user.get_status_display(),
            'email_verified': user.email_verified,
            'date_joined': user.date_joined.isoformat() if user.date_joined else None,
            'last_login': user.last_login.isoformat() if user.last_login else None,
            'last_login_ip': user.last_login_ip,
            'is_active': user.is_active,
        }
    
    @staticmethod
    def _get_profile_data(user: CustomUser) -> Dict[str, Any]:
        """Get user profile data"""
        profile_data = {}
        
        # Get basic user profile
        try:
            user_profile = user.user_profile
            profile_data['basic_profile'] = {
                'first_name': user_profile.first_name,
                'last_name': user_profile.last_name,
                'phone_number': user_profile.phone_number,
                'birth_date': user_profile.birth_date.isoformat() if user_profile.birth_date else None,
                'gender': user_profile.get_gender_display() if user_profile.gender else None,
                'bio': user_profile.bio,
                'created_at': user_profile.created_at.isoformat(),
                'updated_at': user_profile.updated_at.isoformat(),
            }
        except:
            pass
        
        # Get customer profile if exists
        if user.is_customer:
            try:
                customer_profile = user.customer_profile
                profile_data['customer_profile'] = {
                    'first_name': customer_profile.first_name,
                    'last_name': customer_profile.last_name,
                    'gender': customer_profile.get_gender_display() if customer_profile.gender else None,
                    'birth_month': customer_profile.get_birth_month_display() if customer_profile.birth_month else None,
                    'birth_year': customer_profile.birth_year,
                    'phone_number': customer_profile.phone_number,
                    'address': customer_profile.address,
                    'city': customer_profile.city,
                    'zip_code': customer_profile.zip_code,
                    'created_at': customer_profile.created_at.isoformat(),
                    'updated_at': customer_profile.updated_at.isoformat(),
                }
            except:
                pass
        
        # Get service provider profile if exists
        if user.is_service_provider:
            try:
                provider_profile = user.service_provider_profile
                profile_data['service_provider_profile'] = {
                    'legal_name': provider_profile.legal_name,
                    'display_name': provider_profile.display_name,
                    'description': provider_profile.description,
                    'phone': provider_profile.phone,
                    'contact_name': provider_profile.contact_name,
                    'address': provider_profile.address,
                    'city': provider_profile.city,
                    'state': provider_profile.get_state_display(),
                    'county': provider_profile.county,
                    'zip_code': provider_profile.zip_code,
                    'ein': provider_profile.ein,
                    'website': provider_profile.website,
                    'instagram': provider_profile.instagram,
                    'facebook': provider_profile.facebook,
                    'is_public': provider_profile.is_public,
                    'created': provider_profile.created.isoformat(),
                    'updated': provider_profile.updated.isoformat(),
                }
                
                # Get team members
                team_members = []
                for member in provider_profile.team.all():
                    team_members.append({
                        'name': member.name,
                        'position': member.position,
                        'is_active': member.is_active,
                        'created': member.created.isoformat(),
                        'updated': member.updated.isoformat(),
                    })
                
                if team_members:
                    profile_data['team_members'] = team_members
                    
            except:
                pass
        
        return profile_data
    
    @staticmethod
    def _get_preferences_data(user: CustomUser) -> Dict[str, Any]:
        """Get user preferences data"""
        try:
            preferences = user.user_preferences
            return {
                'theme_preference': preferences.get_theme_preference_display(),
                'language_preference': preferences.get_language_preference_display(),
                'timezone_preference': preferences.get_timezone_preference_display(),
                'notification_preference': preferences.get_notification_preference_display(),
                'email_notifications': preferences.email_notifications,
                'marketing_emails': preferences.marketing_emails,
                'created_at': preferences.created_at.isoformat(),
                'updated_at': preferences.updated_at.isoformat(),
            }
        except:
            return {}
    
    @staticmethod
    def _get_security_data(user: CustomUser) -> Dict[str, Any]:
        """Get user security data (anonymized)"""
        try:
            security = user.user_security
            return {
                'two_factor_enabled': security.is_two_factor_enabled,
                'two_factor_method': security.get_two_factor_method_display(),
                'last_password_change': security.last_password_change.isoformat(),
                'failed_login_attempts': security.failed_login_attempts,
                'account_locked': security.is_account_locked,
                'last_activity': security.last_activity.isoformat() if security.last_activity else None,
                'created_at': security.created_at.isoformat(),
                'updated_at': security.updated_at.isoformat(),
            }
        except:
            return {}
    
    @staticmethod
    def _get_activity_history(user: CustomUser) -> Dict[str, Any]:
        """Get user activity history"""
        activity_data = {}
        
        # Get login history (last 100 entries)
        try:
            login_history = []
            for login in user.login_history.all()[:100]:
                login_history.append({
                    'timestamp': login.timestamp.isoformat(),
                    'ip_address': login.ip_address,
                    'user_agent': login.user_agent,
                    'is_successful': login.is_successful,
                })
            
            if login_history:
                activity_data['login_history'] = login_history
        except:
            pass
        
        # Get password history count (not actual passwords)
        try:
            password_count = user.password_history.count()
            if password_count > 0:
                activity_data['password_changes'] = {
                    'total_password_changes': password_count,
                    'last_password_change': user.password_history.first().created_at.isoformat()
                }
        except:
            pass
        
        return activity_data
    
    @staticmethod
    def _get_verification_data(user: CustomUser) -> Dict[str, Any]:
        """Get user verification data"""
        try:
            verifications = []
            for verification in user.verifications.all():
                verifications.append({
                    'verification_type': verification.get_verification_type_display(),
                    'status': verification.get_status_display(),
                    'submitted_at': verification.submitted_at.isoformat(),
                    'verified_at': verification.verified_at.isoformat() if verification.verified_at else None,
                    'expires_at': verification.expires_at.isoformat() if verification.expires_at else None,
                    'verified_by': verification.verified_by.email if verification.verified_by else None,
                })
            
            return {'verifications': verifications} if verifications else {}
        except:
            return {}
    
    @staticmethod
    def _get_privacy_settings(user: CustomUser) -> Dict[str, Any]:
        """Get user privacy settings"""
        try:
            privacy = user.privacy_settings
            return {
                'profile_visibility': privacy.get_profile_visibility_display(),
                'show_email': privacy.show_email,
                'show_phone': privacy.show_phone,
                'show_address': privacy.show_address,
                'show_birth_date': privacy.show_birth_date,
                'show_gender': privacy.show_gender,
                'show_profile_picture': privacy.show_profile_picture,
                'show_last_seen': privacy.show_last_seen,
                'show_booking_history': privacy.show_booking_history,
                'show_reviews': privacy.show_reviews,
                'show_favorites': privacy.show_favorites,
                'discoverable_in_search': privacy.discoverable_in_search,
                'allow_friend_requests': privacy.allow_friend_requests,
                'allow_messages': privacy.allow_messages,
                'allow_analytics': privacy.allow_analytics,
                'allow_personalized_ads': privacy.allow_personalized_ads,
                'data_processing_consent': privacy.data_processing_consent,
                'marketing_consent': privacy.marketing_consent,
                'third_party_sharing': privacy.third_party_sharing,
                'created_at': privacy.created_at.isoformat(),
                'updated_at': privacy.updated_at.isoformat(),
            }
        except:
            return {}
    
    @staticmethod
    def _get_business_data(user: CustomUser) -> Dict[str, Any]:
        """Get business-related data for service providers"""
        if not user.is_service_provider:
            return {}
        
        business_data = {}
        
        # This would include bookings, reviews, etc.
        # For now, we'll just return a placeholder
        try:
            business_data['note'] = "Additional business data (bookings, reviews, etc.) would be included here based on your other app models."
        except:
            pass
        
        return business_data
    
    @staticmethod
    def _get_email_preferences(user: CustomUser) -> Dict[str, Any]:
        """Get email preferences data"""
        try:
            email_prefs = user.email_preferences
            return {
                'email_notifications_enabled': email_prefs.email_notifications_enabled,
                'account_notifications': email_prefs.account_notifications,
                'booking_notifications': email_prefs.booking_notifications,
                'payment_notifications': email_prefs.payment_notifications,
                'marketing_emails': email_prefs.marketing_emails,
                'event_notifications': email_prefs.event_notifications,
                'review_notifications': email_prefs.review_notifications,
                'digest_frequency': email_prefs.get_digest_frequency_display(),
                'email_format': email_prefs.get_email_format_display(),
                'unsubscribe_all': email_prefs.unsubscribe_all,
                'unsubscribe_date': email_prefs.unsubscribe_date.isoformat() if email_prefs.unsubscribe_date else None,
                'created_at': email_prefs.created_at.isoformat(),
                'updated_at': email_prefs.updated_at.isoformat(),
            }
        except:
            return {}
    
    @staticmethod
    def generate_data_export_file(user: CustomUser, file_format='json', request=None) -> Tuple[bool, Optional[str], str]:
        """
        Generate a data export file for download.
        
        Args:
            user: User object
            file_format: Export format ('json', 'csv', 'xml')
            request: HTTP request object
            
        Returns:
            Tuple of (success, file_path, message)
        """
        try:
            # Get user data
            success, user_data, message = GDPRService.export_user_data(user, request)
            
            if not success:
                return False, None, message
            
            # Create export directory if it doesn't exist
            export_dir = os.path.join(settings.MEDIA_ROOT, 'data_exports')
            os.makedirs(export_dir, exist_ok=True)
            
            # Generate filename
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"user_data_{user.id}_{timestamp}.{file_format}"
            file_path = os.path.join(export_dir, filename)
            
            # Generate file based on format
            if file_format == 'json':
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(user_data, f, indent=2, ensure_ascii=False)
            
            elif file_format == 'csv':
                # Flatten the data for CSV
                flat_data = GDPRService._flatten_dict(user_data)
                with open(file_path, 'w', newline='', encoding='utf-8') as f:
                    writer = csv.writer(f)
                    writer.writerow(['Field', 'Value'])
                    for key, value in flat_data.items():
                        writer.writerow([key, str(value)])
            
            elif file_format == 'xml':
                root = ET.Element('UserDataExport')
                GDPRService._dict_to_xml(user_data, root)
                tree = ET.ElementTree(root)
                tree.write(file_path, encoding='utf-8', xml_declaration=True)
            
            # Log the export
            log_user_activity(
                activity_type='data_export_file_generated',
                user=user,
                request=request,
                details={'file_format': file_format, 'file_size': os.path.getsize(file_path)}
            )
            
            return True, file_path, _("Data export file generated successfully")
            
        except Exception as e:
            logger.error(f"Data export file generation error: {str(e)}", exc_info=True)
            return False, None, _("Failed to generate export file. Please try again.")
    
    @staticmethod
    def _flatten_dict(data: Dict[str, Any], parent_key: str = '', sep: str = '.') -> Dict[str, Any]:
        """Flatten nested dictionary for CSV export"""
        items = []
        for k, v in data.items():
            new_key = f"{parent_key}{sep}{k}" if parent_key else k
            if isinstance(v, dict):
                items.extend(GDPRService._flatten_dict(v, new_key, sep=sep).items())
            elif isinstance(v, list):
                for i, item in enumerate(v):
                    if isinstance(item, dict):
                        items.extend(GDPRService._flatten_dict(item, f"{new_key}[{i}]", sep=sep).items())
                    else:
                        items.append((f"{new_key}[{i}]", item))
            else:
                items.append((new_key, v))
        return dict(items)
    
    @staticmethod
    def _dict_to_xml(data: Dict[str, Any], parent: ET.Element):
        """Convert dictionary to XML elements"""
        for key, value in data.items():
            if isinstance(value, dict):
                element = ET.SubElement(parent, key)
                GDPRService._dict_to_xml(value, element)
            elif isinstance(value, list):
                for i, item in enumerate(value):
                    element = ET.SubElement(parent, f"{key}_item")
                    if isinstance(item, dict):
                        GDPRService._dict_to_xml(item, element)
                    else:
                        element.text = str(item)
            else:
                element = ET.SubElement(parent, key)
                element.text = str(value) if value is not None else ''
    
    @staticmethod
    def schedule_data_deletion(user: CustomUser, deletion_date: timezone.datetime = None, request=None) -> Tuple[bool, str]:
        """
        Schedule user data deletion (for GDPR "right to be forgotten").
        
        Args:
            user: User object
            deletion_date: When to delete the data (defaults to 30 days from now)
            request: HTTP request object
            
        Returns:
            Tuple of (success, message)
        """
        try:
            if deletion_date is None:
                deletion_date = timezone.now() + timedelta(days=30)
            
            # Log the deletion request
            log_user_activity(
                activity_type='data_deletion_scheduled',
                user=user,
                request=request,
                details={'scheduled_deletion_date': deletion_date.isoformat()}
            )
            
            # In a real implementation, you would:
            # 1. Mark the user for deletion
            # 2. Set up a scheduled task to delete the data
            # 3. Send confirmation email
            # 4. Handle data retention requirements
            
            logger.info(f"Data deletion scheduled for user {user.email} on {deletion_date}")
            return True, _("Data deletion has been scheduled successfully")
            
        except Exception as e:
            logger.error(f"Data deletion scheduling error: {str(e)}", exc_info=True)
            return False, _("Failed to schedule data deletion. Please try again.")
    
    @staticmethod
    def get_data_portability_info(user: CustomUser) -> Dict[str, Any]:
        """
        Get information about data portability for the user.
        
        Args:
            user: User object
            
        Returns:
            Dictionary with portability information
        """
        try:
            # Get export history
            export_history = []
            # In a real implementation, you would track export history
            
            portability_info = {
                'user_email': user.email,
                'data_categories': [
                    'Account Information',
                    'Profile Data',
                    'Preferences',
                    'Security Settings',
                    'Activity History',
                    'Verification Data',
                    'Privacy Settings',
                    'Email Preferences',
                ],
                'export_formats': ['JSON', 'CSV', 'XML'],
                'export_history': export_history,
                'retention_period': '7 years for tax purposes, 2 years for analytics',
                'last_updated': timezone.now().isoformat(),
            }
            
            if user.is_service_provider:
                portability_info['data_categories'].extend([
                    'Business Profile',
                    'Team Members',
                    'Business Settings',
                ])
            
            return portability_info
            
        except Exception as e:
            logger.error(f"Data portability info error: {str(e)}", exc_info=True)
            return {} 