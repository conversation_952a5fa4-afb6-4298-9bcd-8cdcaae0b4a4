"""
Security service for accounts_app.

This module contains the SecurityService class for handling
security-related operations.
"""

import logging
from typing import Dict, <PERSON><PERSON>, Any, Optional
from django.utils.translation import gettext_lazy as _

# Local imports
from ...models import CustomUser, UserSecurity, AccountLockout
from ...utils import log_error


logger = logging.getLogger(__name__)


class SecurityService:
    """
    Service for handling security-related operations.
    
    Provides methods for lockout management, security monitoring,
    and threat detection.
    """
    
    @staticmethod
    def check_account_lockout(user: CustomUser, ip_address: str = None) -> Tuple[bool, str]:
        """
        Check if account or IP is locked.
        
        Args:
            user: User object
            ip_address: IP address to check
            
        Returns:
            Tuple of (is_locked, message)
        """
        try:
            # Check IP lockout
            if ip_address and AccountLockout.is_ip_locked(ip_address):
                return True, _("Account is temporarily locked due to multiple failed attempts")
            
            # Check user-specific lockout
            if user:
                security = UserSecurity.objects.filter(user=user).first()
                if security and security.is_account_locked:
                    return True, _("Account is temporarily locked")
            
            return False, ""
            
        except Exception as e:
            logger.error(f"Account lockout check error: {str(e)}", exc_info=True)
            return False, ""
    
    @staticmethod
    def record_security_event(event_type: str, user: CustomUser = None, ip_address: str = None, 
                            details: Dict[str, Any] = None, request=None) -> bool:
        """
        Record security event for monitoring.
        
        Args:
            event_type: Type of security event
            user: User object (optional)
            ip_address: IP address
            details: Additional event details
            request: HTTP request object
            
        Returns:
            Boolean indicating success
        """
        try:
            # Log security event
            log_error(
                error_type='security_event',
                error_message=f"Security event: {event_type}",
                user=user,
                request=request,
                details=details or {}
            )
            
            logger.warning(f"Security event recorded: {event_type}, user: {user.email if user else 'N/A'}, IP: {ip_address}")
            return True
            
        except Exception as e:
            logger.error(f"Security event recording error: {str(e)}", exc_info=True)
            return False


class AccountLockoutService:
    """
    Service for handling account lockout operations.
    
    Provides methods for managing account lockouts, IP-based lockouts,
    and lockout duration calculations.
    """
    
    @staticmethod
    def lock_account(user: CustomUser, duration_minutes: int = None, ip_address: str = None) -> bool:
        """
        Lock an account for a specified duration.
        
        Args:
            user: User object to lock
            duration_minutes: Duration in minutes (optional)
            ip_address: IP address to lock (optional)
            
        Returns:
            Boolean indicating success
        """
        try:
            # Lock user account
            if user:
                security = UserSecurity.objects.get_or_create(user=user)[0]
                security.lock_account(duration_minutes)
            
            # Lock IP address
            if ip_address:
                AccountLockout.record_failed_attempt(ip_address, user)
            
            logger.warning(f"Account locked: {user.email if user else 'N/A'}, IP: {ip_address}")
            return True
            
        except Exception as e:
            logger.error(f"Account lockout error: {str(e)}", exc_info=True)
            return False
    
    @staticmethod
    def unlock_account(user: CustomUser = None, ip_address: str = None) -> bool:
        """
        Unlock an account or IP address.
        
        Args:
            user: User object to unlock (optional)
            ip_address: IP address to unlock (optional)
            
        Returns:
            Boolean indicating success
        """
        try:
            # Unlock user account
            if user:
                security = UserSecurity.objects.filter(user=user).first()
                if security:
                    security.unlock_account()
            
            # Unlock IP address
            if ip_address:
                AccountLockout.reset_failed_attempts(ip_address)
            
            logger.info(f"Account unlocked: {user.email if user else 'N/A'}, IP: {ip_address}")
            return True
            
        except Exception as e:
            logger.error(f"Account unlock error: {str(e)}", exc_info=True)
            return False
    
    @staticmethod
    def is_locked(user: CustomUser = None, ip_address: str = None) -> bool:
        """
        Check if account or IP is currently locked.
        
        Args:
            user: User object to check (optional)
            ip_address: IP address to check (optional)
            
        Returns:
            Boolean indicating if locked
        """
        try:
            # Check user lockout
            if user:
                security = UserSecurity.objects.filter(user=user).first()
                if security and security.is_account_locked:
                    return True
            
            # Check IP lockout
            if ip_address and AccountLockout.is_ip_locked(ip_address):
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Lock status check error: {str(e)}", exc_info=True)
            return False


class TwoFactorService:
    """
    Service for handling two-factor authentication operations.
    
    Provides methods for enabling, disabling, and verifying
    two-factor authentication.
    """
    
    @staticmethod
    def enable_two_factor(user: CustomUser, method: str) -> bool:
        """
        Enable two-factor authentication for a user.
        
        Args:
            user: User object
            method: Two-factor method (sms, email, authenticator)
            
        Returns:
            Boolean indicating success
        """
        try:
            security = UserSecurity.objects.get_or_create(user=user)[0]
            security.two_factor_method = method
            security.save()
            
            logger.info(f"Two-factor enabled for user: {user.email}, method: {method}")
            return True
            
        except Exception as e:
            logger.error(f"Two-factor enable error: {str(e)}", exc_info=True)
            return False
    
    @staticmethod
    def disable_two_factor(user: CustomUser) -> bool:
        """
        Disable two-factor authentication for a user.
        
        Args:
            user: User object
            
        Returns:
            Boolean indicating success
        """
        try:
            security = UserSecurity.objects.filter(user=user).first()
            if security:
                security.two_factor_method = 'disabled'
                security.save()
            
            logger.info(f"Two-factor disabled for user: {user.email}")
            return True
            
        except Exception as e:
            logger.error(f"Two-factor disable error: {str(e)}", exc_info=True)
            return False
    
    @staticmethod
    def is_two_factor_enabled(user: CustomUser) -> bool:
        """
        Check if two-factor authentication is enabled for a user.
        
        Args:
            user: User object
            
        Returns:
            Boolean indicating if two-factor is enabled
        """
        try:
            security = UserSecurity.objects.filter(user=user).first()
            if security:
                return security.is_two_factor_enabled
            return False
            
        except Exception as e:
            logger.error(f"Two-factor status check error: {str(e)}", exc_info=True)
            return False 