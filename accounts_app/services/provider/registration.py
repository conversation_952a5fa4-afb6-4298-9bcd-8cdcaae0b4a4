"""
Provider registration service for accounts_app.

This module contains the ProviderRegistrationService class for handling
service provider registration operations.
"""

# --- Third-Party Imports ---
import logging
from typing import Dict, Optional, Tuple, Any
from django.core.exceptions import ValidationError
from django.db import transaction
from django.utils.translation import gettext_lazy as _

# Local imports
from ...constants import UserRoles
from ...models import (
    CustomUser,
    ServiceProviderProfile,
    UserPreferences,
    UserSecurity,
    EmailPreferences,
)
from ...utils import log_account_lifecycle_event


logger = logging.getLogger(__name__)


class ProviderRegistrationService:
    """
    Service for handling service provider registration operations.

    Provides methods for service provider registration with automatic profile creation
    and email verification.
    """

    @staticmethod
    def is_registration_open(request=None) -> bool:
        """
        Check if registration is currently open.

        Args:
            request: HTTP request object (optional)

        Returns:
            bool: True if registration is open
        """
        # Add any business logic for registration availability
        # For now, always return True (registration is open)
        return True

    @staticmethod
    def register_service_provider(user_data: Dict[str, Any], request=None) -> <PERSON>ple[bool, Optional[CustomUser], str]:
        """
        Register a new service provider user.
        
        Args:
            user_data: Dictionary containing user registration data
            request: HTTP request object for logging
            
        Returns:
            Tuple of (success, user_object, message)
        """
        try:
            with transaction.atomic():
                # Create user account (active but requires email verification)
                user = CustomUser.objects.create_user(
                    email=user_data['email'],
                    password=user_data['password'],
                    first_name=user_data.get('first_name', ''),
                    last_name=user_data.get('last_name', ''),
                    role=UserRoles.SERVICE_PROVIDER,
                    is_active=True  # Active but requires email verification
                )
                
                # Create service provider profile
                ServiceProviderProfile.objects.create(
                    user=user,
                    legal_name=user_data['business_name'],
                    phone=user_data.get('business_phone_number', ''),  # Default to empty, can be added later
                    contact_name=user_data['contact_person_name'],
                    address=user_data.get('business_address', ''),  # Default to empty, can be added later
                    city=user_data.get('city', ''),  # Default to empty, can be added later
                    state=user_data.get('state', ''),  # Default to empty, can be added later
                    zip_code=user_data.get('zip_code', ''),  # Default to empty, can be added later
                    ein=user_data.get('ein', '')
                )
                
                # Create user preferences with defaults
                UserPreferences.objects.create(user=user)
                
                # Create user security record
                UserSecurity.objects.create(user=user)
                
                # Create email preferences with defaults
                EmailPreferences.objects.create(user=user)
                
                # Send verification email - importing here to avoid circular import
                from ..common.email import EmailService
                EmailService.send_verification_email(user, request)
                
                # Log account creation
                log_account_lifecycle_event(
                    event_type='creation',
                    user=user,
                    request=request,
                    reason='provider_registration'
                )
                
                logger.info(f"Service provider account created successfully: {user.email}")
                return True, user, _("Account created successfully. Please check your email to verify your account.")
                
        except ValidationError as e:
            logger.error(f"Service provider registration validation error: {str(e)}")
            return False, None, _("Registration data is invalid")
        except Exception as e:
            logger.error(f"Service provider registration error: {str(e)}", exc_info=True)
            return False, None, _("Registration failed. Please try again.") 