"""
Customer registration service for accounts_app.

This module contains the CustomerRegistrationService class for handling
customer registration operations.
"""

# --- Third-Party Imports ---
import logging
from typing import Dict, Optional, Tuple, Any
from django.core.exceptions import ValidationError
from django.db import transaction
from django.utils.translation import gettext_lazy as _

# Local imports
from ...constants import UserRoles
from ...models import (
    CustomUser,
    CustomerProfile,
    UserPreferences,
    UserSecurity,
    EmailPreferences,
)
from ...utils import log_account_lifecycle_event


logger = logging.getLogger(__name__)


class CustomerRegistrationService:
    """
    Service for handling customer registration operations.

    Provides methods for customer registration with automatic profile creation.
    """

    @staticmethod
    def is_registration_open(request=None) -> bool:
        """
        Check if registration is currently open.

        Args:
            request: HTTP request object (optional)

        Returns:
            bool: True if registration is open
        """
        # Add any business logic for registration availability
        # For now, always return True (registration is open)
        return True

    @staticmethod
    def register_customer(user_data: Dict[str, Any], request=None) -> Tuple[bool, Optional[CustomUser], str]:
        """
        Register a new customer user.
        
        Args:
            user_data: Dictionary containing user registration data
            request: HTTP request object for logging
            
        Returns:
            Tuple of (success, user_object, message)
        """
        try:
            with transaction.atomic():
                # Create user account
                user = CustomUser.objects.create_user(
                    email=user_data['email'],
                    password=user_data['password'],
                    first_name=user_data.get('first_name', ''),
                    last_name=user_data.get('last_name', ''),
                    role=UserRoles.CUSTOMER
                )
                
                # Create customer profile
                CustomerProfile.objects.create(
                    user=user,
                    first_name=user_data.get('first_name', ''),
                    last_name=user_data.get('last_name', ''),
                    phone_number=user_data.get('phone_number', ''),
                    gender=user_data.get('gender', ''),
                    birth_month=user_data.get('birth_month'),
                    birth_year=user_data.get('birth_year'),
                )
                
                # Create user preferences with defaults
                UserPreferences.objects.create(user=user)
                
                # Create user security record
                UserSecurity.objects.create(user=user)
                
                # Create email preferences with defaults
                EmailPreferences.objects.create(user=user)
                
                # Send welcome email for customers (no verification needed)
                try:
                    from utility_app.email_utils import send_welcome_email
                    user_name = user.get_full_name() or user.email
                    send_welcome_email(user.email, user_name, 'customer')
                    logger.info(f"Welcome email sent to customer: {user.email}")
                except Exception as e:
                    logger.warning(f"Failed to send welcome email to customer {user.email}: {str(e)}")
                
                # Log account creation
                log_account_lifecycle_event(
                    event_type='creation',
                    user=user,
                    request=request,
                    reason='customer_registration'
                )
                
                logger.info(f"Customer account created successfully: {user.email}")
                return True, user, _("Account created successfully. Welcome to CozyWish!")
                
        except ValidationError as e:
            logger.error(f"Customer registration validation error: {str(e)}")
            return False, None, _("Registration data is invalid")
        except Exception as e:
            logger.error(f"Customer registration error: {str(e)}", exc_info=True)
            return False, None, _("Registration failed. Please try again.") 