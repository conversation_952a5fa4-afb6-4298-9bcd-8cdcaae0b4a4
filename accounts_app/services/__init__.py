"""
Service layer for accounts_app.

This module provides a unified interface to all account-related services,
including authentication, registration, profile management, and more.
"""

# Import common services (shared between customers and providers)
from .common import (
    AuthenticationService, AuthenticationError, get_client_ip,
    AccountService, PasswordService, SecurityService, 
    AccountLockoutService, TwoFactorService, EmailService, 
    EmailVerificationService, GDPRService, SessionService
)

# Import customer-specific services
from .customer import CustomerProfileService, CustomerRegistrationService

# Import provider-specific services
from .provider import ProviderProfileService, ProviderRegistrationService, TeamService

# Import utility functions and exceptions
# Import base exception classes that might be used across the codebase
class ServiceError(Exception):
    """Base exception for service layer errors"""
    pass

class ProfileError(ServiceError):
    """Profile-related errors"""
    pass

class SecurityError(ServiceError):
    """Security-related errors"""
    pass

__all__ = [
    # Common services
    'AuthenticationService',
    'AuthenticationError',
    'AccountService',
    'PasswordService',
    'EmailService',
    'EmailVerificationService',
    'TeamService',
    'SecurityService',
    'AccountLockoutService',
    'TwoFactorService',
    'GDPRService',
    'SessionService',
    'get_client_ip',
    
    # Customer services
    'CustomerProfileService',
    'CustomerRegistrationService',
    
    # Provider services
    'ProviderProfileService',
    'ProviderRegistrationService',
    
    # Exceptions
    'ServiceError',
    'ProfileError',
    'SecurityError',
] 