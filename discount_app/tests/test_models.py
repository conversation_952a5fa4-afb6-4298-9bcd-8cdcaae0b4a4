"""
Unit tests for discount_app models.

This module contains comprehensive unit tests for all model classes in the discount_app,
including DiscountBase, VenueDiscount, ServiceDiscount, PlatformDiscount, and DiscountUsage.
"""

# Standard library imports
from datetime import timedelta
from decimal import Decimal
from unittest.mock import patch, Mock

# Django imports
from django.test import TestCase, override_settings
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from django.utils import timezone
from django.db import IntegrityError

# Local imports
from discount_app.models import (
    DiscountType, DiscountStatus, DiscountBase, VenueDiscount, 
    ServiceDiscount, PlatformDiscount, DiscountUsage
)
from venues_app.models import Category, Venue, Service
from accounts_app.models.provider import ServiceProviderProfile

User = get_user_model()


class DiscountTypeChoicesTest(TestCase):
    """Test the DiscountType choices."""

    def test_discount_type_choices(self):
        """Test that discount type choices are properly defined."""
        expected_choices = [
            ('percentage', 'Percentage'),
            ('fixed_amount', 'Fixed Amount')
        ]
        self.assertEqual(DiscountType.choices, expected_choices)

    def test_discount_type_values(self):
        """Test discount type constant values."""
        self.assertEqual(DiscountType.PERCENTAGE, 'percentage')
        self.assertEqual(DiscountType.FIXED_AMOUNT, 'fixed_amount')


class DiscountStatusChoicesTest(TestCase):
    """Test the DiscountStatus choices."""

    def test_discount_status_choices(self):
        """Test that discount status choices are properly defined."""
        expected_choices = [
            ('active', 'Active'),
            ('scheduled', 'Scheduled'),
            ('expired', 'Expired'),
            ('cancelled', 'Cancelled')
        ]
        self.assertEqual(DiscountStatus.choices, expected_choices)

    def test_discount_status_values(self):
        """Test discount status constant values."""
        self.assertEqual(DiscountStatus.ACTIVE, 'active')
        self.assertEqual(DiscountStatus.SCHEDULED, 'scheduled')
        self.assertEqual(DiscountStatus.EXPIRED, 'expired')
        self.assertEqual(DiscountStatus.CANCELLED, 'cancelled')


class DiscountBaseTest(TestCase):
    """Test the abstract DiscountBase model functionality."""

    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.user.role = User.SERVICE_PROVIDER
        self.user.save()

        # Create category
        self.category = Category.objects.create(
            category_name='Spa Services',
            category_description='Relaxing spa treatments',
            is_active=True
        )

        # Create service provider profile
        self.provider_profile = ServiceProviderProfile.objects.create(
            user=self.user,
            legal_name='Test Spa',
            phone='+**********',
            contact_name='John Doe',
            address='123 Business St',
            city='New York',
            state='NY',
            zip_code='10001'
        )

        # Create venue
        self.venue = Venue.objects.create(
            service_provider=self.provider_profile,
            venue_name='Test Spa Venue',
            short_description='A relaxing spa venue for all your wellness needs',
            street_number='123',
            street_name='Spa St',
            city='New York',
            county='Manhattan',
            state='NY',
            phone='+**********',
            approval_status=Venue.APPROVED
        )
        # Add category relationship
        self.venue.categories.add(self.category)

        # Create service
        self.service = Service.objects.create(
            venue=self.venue,
            service_title='Massage Therapy',
            short_description='Relaxing massage',
            price_min=Decimal('100.00'),
            price_max=Decimal('200.00'),
            duration_minutes=60,
            is_active=True
        )

        # Base discount data
        self.discount_data = {
            'name': 'Test Discount',
            'description': 'Test discount description',
            'discount_type': DiscountType.PERCENTAGE,
            'discount_value': Decimal('20.00'),
            'start_date': timezone.now() + timedelta(hours=1),
            'end_date': timezone.now() + timedelta(days=7),
            'created_by': self.user
        }

    def test_discount_base_is_abstract(self):
        """Test that DiscountBase is abstract and cannot be instantiated."""
        with self.assertRaises(TypeError):
            DiscountBase.objects.create(**self.discount_data)

    def test_discount_base_string_representation(self):
        """Test discount base string representation through concrete model."""
        service_discount = ServiceDiscount.objects.create(
            service=self.service,
            **self.discount_data
        )
        self.assertEqual(str(service_discount), 'Test Discount')

    def test_clean_valid_percentage_discount(self):
        """Test clean method with valid percentage discount."""
        service_discount = ServiceDiscount(
            service=self.service,
            discount_type=DiscountType.PERCENTAGE,
            discount_value=Decimal('50.00'),
            **{k: v for k, v in self.discount_data.items() if k != 'discount_type' and k != 'discount_value'}
        )
        # Should not raise ValidationError
        service_discount.clean()

    def test_clean_invalid_percentage_over_80(self):
        """Test clean method with percentage over 80%."""
        service_discount = ServiceDiscount(
            service=self.service,
            discount_type=DiscountType.PERCENTAGE,
            discount_value=Decimal('85.00'),
            **{k: v for k, v in self.discount_data.items() if k != 'discount_type' and k != 'discount_value'}
        )
        with self.assertRaises(ValidationError) as context:
            service_discount.clean()
        self.assertIn('Percentage discount cannot exceed 80%', str(context.exception))

    def test_clean_invalid_percentage_over_100(self):
        """Test clean method with percentage over 100%."""
        service_discount = ServiceDiscount(
            service=self.service,
            discount_type=DiscountType.PERCENTAGE,
            discount_value=Decimal('150.00'),
            **{k: v for k, v in self.discount_data.items() if k != 'discount_type' and k != 'discount_value'}
        )
        with self.assertRaises(ValidationError) as context:
            service_discount.clean()
        self.assertIn('Percentage must be between 0.01 and 80', str(context.exception))

    def test_clean_invalid_percentage_zero(self):
        """Test clean method with zero percentage."""
        service_discount = ServiceDiscount(
            service=self.service,
            discount_type=DiscountType.PERCENTAGE,
            discount_value=Decimal('0.00'),
            **{k: v for k, v in self.discount_data.items() if k != 'discount_type' and k != 'discount_value'}
        )
        with self.assertRaises(ValidationError) as context:
            service_discount.clean()
        self.assertIn('Percentage must be between 0.01 and 80', str(context.exception))

    def test_clean_valid_fixed_amount_discount(self):
        """Test clean method with valid fixed amount discount."""
        service_discount = ServiceDiscount(
            service=self.service,
            discount_type=DiscountType.FIXED_AMOUNT,
            discount_value=Decimal('25.00'),
            **{k: v for k, v in self.discount_data.items() if k != 'discount_type' and k != 'discount_value'}
        )
        # Should not raise ValidationError
        service_discount.clean()

    def test_clean_invalid_fixed_amount_zero(self):
        """Test clean method with zero fixed amount."""
        service_discount = ServiceDiscount(
            service=self.service,
            discount_type=DiscountType.FIXED_AMOUNT,
            discount_value=Decimal('0.00'),
            **{k: v for k, v in self.discount_data.items() if k != 'discount_type' and k != 'discount_value'}
        )
        with self.assertRaises(ValidationError) as context:
            service_discount.clean()
        self.assertIn('Fixed amount must be greater than 0', str(context.exception))

    def test_clean_invalid_date_range(self):
        """Test clean method with invalid date range."""
        service_discount = ServiceDiscount(
            service=self.service,
            start_date=timezone.now() + timedelta(days=7),
            end_date=timezone.now() + timedelta(hours=1),
            **{k: v for k, v in self.discount_data.items() if k != 'start_date' and k != 'end_date'}
        )
        with self.assertRaises(ValidationError) as context:
            service_discount.clean()
        self.assertIn('Start date must be before end date', str(context.exception))

    def test_is_active_method_active_discount(self):
        """Test is_active method with currently active discount."""
        service_discount = ServiceDiscount.objects.create(
            service=self.service,
            start_date=timezone.now() - timedelta(hours=1),
            end_date=timezone.now() + timedelta(hours=1),
            **{k: v for k, v in self.discount_data.items() if k != 'start_date' and k != 'end_date'}
        )
        self.assertTrue(service_discount.is_active())

    def test_is_active_method_future_discount(self):
        """Test is_active method with future discount."""
        service_discount = ServiceDiscount.objects.create(
            service=self.service,
            start_date=timezone.now() + timedelta(hours=1),
            end_date=timezone.now() + timedelta(days=1),
            **{k: v for k, v in self.discount_data.items() if k != 'start_date' and k != 'end_date'}
        )
        self.assertFalse(service_discount.is_active())

    def test_is_active_method_expired_discount(self):
        """Test is_active method with expired discount."""
        service_discount = ServiceDiscount.objects.create(
            service=self.service,
            start_date=timezone.now() - timedelta(days=2),
            end_date=timezone.now() - timedelta(hours=1),
            **{k: v for k, v in self.discount_data.items() if k != 'start_date' and k != 'end_date'}
        )
        self.assertFalse(service_discount.is_active())

    def test_get_status_scheduled(self):
        """Test get_status method for scheduled discount."""
        service_discount = ServiceDiscount.objects.create(
            service=self.service,
            start_date=timezone.now() + timedelta(hours=1),
            end_date=timezone.now() + timedelta(days=1),
            **{k: v for k, v in self.discount_data.items() if k != 'start_date' and k != 'end_date'}
        )
        self.assertEqual(service_discount.get_status(), DiscountStatus.SCHEDULED)

    def test_get_status_active(self):
        """Test get_status method for active discount."""
        service_discount = ServiceDiscount.objects.create(
            service=self.service,
            start_date=timezone.now() - timedelta(hours=1),
            end_date=timezone.now() + timedelta(hours=1),
            **{k: v for k, v in self.discount_data.items() if k != 'start_date' and k != 'end_date'}
        )
        self.assertEqual(service_discount.get_status(), DiscountStatus.ACTIVE)

    def test_get_status_expired(self):
        """Test get_status method for expired discount."""
        service_discount = ServiceDiscount.objects.create(
            service=self.service,
            start_date=timezone.now() - timedelta(days=2),
            end_date=timezone.now() - timedelta(hours=1),
            **{k: v for k, v in self.discount_data.items() if k != 'start_date' and k != 'end_date'}
        )
        self.assertEqual(service_discount.get_status(), DiscountStatus.EXPIRED)

    def test_calculate_discount_amount_percentage(self):
        """Test calculate_discount_amount method with percentage discount."""
        service_discount = ServiceDiscount.objects.create(
            service=self.service,
            discount_type=DiscountType.PERCENTAGE,
            discount_value=Decimal('20.00'),
            **{k: v for k, v in self.discount_data.items() if k != 'discount_type' and k != 'discount_value'}
        )
        original_price = Decimal('100.00')
        discount_amount = service_discount.calculate_discount_amount(original_price)
        self.assertEqual(discount_amount, Decimal('20.00'))

    def test_calculate_discount_amount_percentage_over_80_capped(self):
        """Test calculate_discount_amount method caps percentage at 80%."""
        # Create discount with 90% but it should be capped at 80%
        service_discount = ServiceDiscount(
            service=self.service,
            discount_type=DiscountType.PERCENTAGE,
            discount_value=Decimal('90.00'),  # This will be capped at 80%
            **{k: v for k, v in self.discount_data.items() if k != 'discount_type' and k != 'discount_value'}
        )
        original_price = Decimal('100.00')
        discount_amount = service_discount.calculate_discount_amount(original_price)
        self.assertEqual(discount_amount, Decimal('80.00'))  # Capped at 80%

    def test_calculate_discount_amount_fixed_amount(self):
        """Test calculate_discount_amount method with fixed amount discount."""
        service_discount = ServiceDiscount.objects.create(
            service=self.service,
            discount_type=DiscountType.FIXED_AMOUNT,
            discount_value=Decimal('25.00'),
            **{k: v for k, v in self.discount_data.items() if k != 'discount_type' and k != 'discount_value'}
        )
        original_price = Decimal('100.00')
        discount_amount = service_discount.calculate_discount_amount(original_price)
        self.assertEqual(discount_amount, Decimal('25.00'))

    def test_calculate_discount_amount_fixed_amount_exceeds_price(self):
        """Test calculate_discount_amount method when fixed amount exceeds price."""
        service_discount = ServiceDiscount.objects.create(
            service=self.service,
            discount_type=DiscountType.FIXED_AMOUNT,
            discount_value=Decimal('150.00'),
            **{k: v for k, v in self.discount_data.items() if k != 'discount_type' and k != 'discount_value'}
        )
        original_price = Decimal('100.00')
        discount_amount = service_discount.calculate_discount_amount(original_price)
        self.assertEqual(discount_amount, Decimal('100.00'))  # Capped at original price

    def test_calculate_discounted_price_percentage(self):
        """Test calculate_discounted_price method with percentage discount."""
        service_discount = ServiceDiscount.objects.create(
            service=self.service,
            discount_type=DiscountType.PERCENTAGE,
            discount_value=Decimal('20.00'),
            **{k: v for k, v in self.discount_data.items() if k != 'discount_type' and k != 'discount_value'}
        )
        original_price = Decimal('100.00')
        discounted_price = service_discount.calculate_discounted_price(original_price)
        self.assertEqual(discounted_price, Decimal('80.00'))

    def test_calculate_discounted_price_fixed_amount(self):
        """Test calculate_discounted_price method with fixed amount discount."""
        service_discount = ServiceDiscount.objects.create(
            service=self.service,
            discount_type=DiscountType.FIXED_AMOUNT,
            discount_value=Decimal('25.00'),
            **{k: v for k, v in self.discount_data.items() if k != 'discount_type' and k != 'discount_value'}
        )
        original_price = Decimal('100.00')
        discounted_price = service_discount.calculate_discounted_price(original_price)
        self.assertEqual(discounted_price, Decimal('75.00'))

    def test_calculate_discounted_price_never_below_zero(self):
        """Test calculate_discounted_price method never goes below zero."""
        service_discount = ServiceDiscount.objects.create(
            service=self.service,
            discount_type=DiscountType.FIXED_AMOUNT,
            discount_value=Decimal('150.00'),
            **{k: v for k, v in self.discount_data.items() if k != 'discount_type' and k != 'discount_value'}
        )
        original_price = Decimal('100.00')
        discounted_price = service_discount.calculate_discounted_price(original_price)
        self.assertEqual(discounted_price, Decimal('0.00'))

    def test_discount_timestamps(self):
        """Test that created_at and updated_at timestamps are set."""
        service_discount = ServiceDiscount.objects.create(
            service=self.service,
            **self.discount_data
        )
        self.assertIsNotNone(service_discount.created_at)
        self.assertIsNotNone(service_discount.updated_at)

        # Test that updated_at changes when discount is saved
        original_updated_at = service_discount.updated_at
        service_discount.name = 'Updated Discount'
        service_discount.save()
        self.assertGreater(service_discount.updated_at, original_updated_at)


class VenueDiscountModelTest(TestCase):
    """Test the VenueDiscount model functionality."""

    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.user.role = User.SERVICE_PROVIDER
        self.user.save()

        # Create category
        self.category = Category.objects.create(
            category_name='Spa Services',
            category_description='Relaxing spa treatments',
            is_active=True
        )

        # Create service provider profile
        self.provider_profile = ServiceProviderProfile.objects.create(
            user=self.user,
            legal_name='Test Spa',
            phone='+**********',
            contact_name='John Doe',
            address='123 Business St',
            city='New York',
            state='NY',
            zip_code='10001'
        )

        # Create venue
        self.venue = Venue.objects.create(
            service_provider=self.provider_profile,
            venue_name='Test Spa Venue',
            short_description='A relaxing spa venue for all your wellness needs',
            street_number='123',
            street_name='Spa St',
            city='New York',
            county='Manhattan',
            state='NY',
            phone='+**********',
            approval_status=Venue.APPROVED
        )
        # Add category relationship
        self.venue.categories.add(self.category)

        # Admin user for approval
        self.admin_user = User.objects.create_user(
            email='<EMAIL>',
            password='adminpass123'
        )
        self.admin_user.role = User.ADMIN
        self.admin_user.save()

        # Base venue discount data
        self.venue_discount_data = {
            'venue': self.venue,
            'name': 'Venue Grand Opening',
            'description': 'Special discount for grand opening',
            'discount_type': DiscountType.PERCENTAGE,
            'discount_value': Decimal('30.00'),
            'start_date': timezone.now() + timedelta(hours=1),
            'end_date': timezone.now() + timedelta(days=7),
            'min_booking_value': Decimal('50.00'),
            'max_discount_amount': Decimal('100.00'),
            'created_by': self.user
        }

    def test_create_venue_discount(self):
        """Test creating a venue discount."""
        venue_discount = VenueDiscount.objects.create(**self.venue_discount_data)

        self.assertEqual(venue_discount.venue, self.venue)
        self.assertEqual(venue_discount.name, 'Venue Grand Opening')
        self.assertEqual(venue_discount.description, 'Special discount for grand opening')
        self.assertEqual(venue_discount.discount_type, DiscountType.PERCENTAGE)
        self.assertEqual(venue_discount.discount_value, Decimal('30.00'))
        self.assertEqual(venue_discount.min_booking_value, Decimal('50.00'))
        self.assertEqual(venue_discount.max_discount_amount, Decimal('100.00'))
        self.assertEqual(venue_discount.created_by, self.user)
        self.assertFalse(venue_discount.is_approved)
        self.assertIsNone(venue_discount.approved_by)
        self.assertIsNone(venue_discount.approved_at)

    def test_venue_discount_string_representation(self):
        """Test venue discount string representation."""
        venue_discount = VenueDiscount.objects.create(**self.venue_discount_data)
        self.assertEqual(str(venue_discount), 'Venue Grand Opening')

    def test_venue_discount_meta_ordering(self):
        """Test venue discount ordering by created_at descending."""
        # Create two discounts with different creation times
        venue_discount1 = VenueDiscount.objects.create(
            name='First Discount',
            **{k: v for k, v in self.venue_discount_data.items() if k != 'name'}
        )
        venue_discount2 = VenueDiscount.objects.create(
            name='Second Discount',
            **{k: v for k, v in self.venue_discount_data.items() if k != 'name'}
        )

        discounts = list(VenueDiscount.objects.all())
        self.assertEqual(discounts[0], venue_discount2)  # Most recent first
        self.assertEqual(discounts[1], venue_discount1)

    def test_venue_discount_clean_valid_ownership(self):
        """Test clean method with valid venue ownership."""
        venue_discount = VenueDiscount(**self.venue_discount_data)
        # Should not raise ValidationError
        venue_discount.clean()

    def test_venue_discount_clean_invalid_ownership(self):
        """Test clean method with invalid venue ownership."""
        # Create another user and venue
        other_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        other_user.role = User.SERVICE_PROVIDER
        other_user.save()

        other_provider_profile = ServiceProviderProfile.objects.create(
            user=other_user,
            legal_name='Other Spa',
            phone='+**********',
            contact_name='Jane Doe',
            address='456 Other St',
            city='Boston',
            state='MA',
            zip_code='02101'
        )

        other_venue = Venue.objects.create(
            service_provider=other_provider_profile,
            venue_name='Other Spa Venue',
            short_description='A relaxing spa venue for all your wellness needs',
            street_number='456',
            street_name='Other St',
            city='Boston',
            county='Suffolk',
            state='MA',
            phone='+**********',
            approval_status=Venue.APPROVED
        )
        # Add category relationship
        other_venue.categories.add(self.category)

        # Try to create discount for other venue
        venue_discount = VenueDiscount(
            venue=other_venue,  # Different venue
            created_by=self.user,  # But same user
            **{k: v for k, v in self.venue_discount_data.items() if k != 'venue' and k != 'created_by'}
        )

        with self.assertRaises(ValidationError) as context:
            venue_discount.clean()
        self.assertIn('You can only create discounts for your own venue', str(context.exception))

    def test_venue_discount_save_approval_timestamp(self):
        """Test save method sets approved_at when approved."""
        venue_discount = VenueDiscount.objects.create(**self.venue_discount_data)

        # Initially not approved
        self.assertFalse(venue_discount.is_approved)
        self.assertIsNone(venue_discount.approved_at)
        self.assertIsNone(venue_discount.approved_by)

        # Approve the discount
        venue_discount.is_approved = True
        venue_discount.approved_by = self.admin_user
        venue_discount.save()

        # Check approval timestamp is set
        self.assertTrue(venue_discount.is_approved)
        self.assertEqual(venue_discount.approved_by, self.admin_user)
        self.assertIsNotNone(venue_discount.approved_at)

    def test_venue_discount_save_unapproval_clears_timestamp(self):
        """Test save method clears approved_at when unapproved."""
        venue_discount = VenueDiscount.objects.create(
            is_approved=True,
            approved_by=self.admin_user,
            **self.venue_discount_data
        )
        venue_discount.approved_at = timezone.now()
        venue_discount.save()

        # Unapprove the discount
        venue_discount.is_approved = False
        venue_discount.save()

        # Check approval data is cleared
        self.assertFalse(venue_discount.is_approved)
        self.assertIsNone(venue_discount.approved_by)
        self.assertIsNone(venue_discount.approved_at)

    def test_venue_discount_is_visible_property_approved_and_active(self):
        """Test is_visible property when discount is approved and active."""
        venue_discount = VenueDiscount.objects.create(
            is_approved=True,
            start_date=timezone.now() - timedelta(hours=1),
            end_date=timezone.now() + timedelta(hours=1),
            **{k: v for k, v in self.venue_discount_data.items() if k != 'start_date' and k != 'end_date'}
        )
        self.assertTrue(venue_discount.is_visible)

    def test_venue_discount_is_visible_property_not_approved(self):
        """Test is_visible property when discount is not approved."""
        venue_discount = VenueDiscount.objects.create(
            is_approved=False,
            start_date=timezone.now() - timedelta(hours=1),
            end_date=timezone.now() + timedelta(hours=1),
            **{k: v for k, v in self.venue_discount_data.items() if k != 'start_date' and k != 'end_date'}
        )
        self.assertFalse(venue_discount.is_visible)

    def test_venue_discount_is_visible_property_not_active(self):
        """Test is_visible property when discount is approved but not active."""
        venue_discount = VenueDiscount.objects.create(
            is_approved=True,
            start_date=timezone.now() + timedelta(hours=1),
            end_date=timezone.now() + timedelta(days=1),
            **{k: v for k, v in self.venue_discount_data.items() if k != 'start_date' and k != 'end_date'}
        )
        self.assertFalse(venue_discount.is_visible)

    def test_venue_discount_min_booking_value_default(self):
        """Test min_booking_value defaults to 0.00."""
        venue_discount_data = {k: v for k, v in self.venue_discount_data.items() if k != 'min_booking_value'}
        venue_discount = VenueDiscount.objects.create(**venue_discount_data)
        self.assertEqual(venue_discount.min_booking_value, Decimal('0.00'))

    def test_venue_discount_max_discount_amount_optional(self):
        """Test max_discount_amount is optional."""
        venue_discount_data = {k: v for k, v in self.venue_discount_data.items() if k != 'max_discount_amount'}
        venue_discount = VenueDiscount.objects.create(**venue_discount_data)
        self.assertIsNone(venue_discount.max_discount_amount)


class ServiceDiscountModelTest(TestCase):
    """Test the ServiceDiscount model functionality."""

    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.user.role = User.SERVICE_PROVIDER
        self.user.save()

        # Create category
        self.category = Category.objects.create(
            category_name='Spa Services',
            category_description='Relaxing spa treatments',
            is_active=True
        )

        # Create service provider profile
        self.provider_profile = ServiceProviderProfile.objects.create(
            user=self.user,
            legal_name='Test Spa',
            phone='+**********',
            contact_name='John Doe',
            address='123 Business St',
            city='New York',
            state='NY',
            zip_code='10001'
        )

        # Create venue
        self.venue = Venue.objects.create(
            service_provider=self.provider_profile,
            venue_name='Test Spa Venue',
            short_description='A relaxing spa venue for all your wellness needs',
            street_number='123',
            street_name='Spa St',
            city='New York',
            county='Manhattan',
            state='NY',
            phone='+**********',
            approval_status=Venue.APPROVED
        )
        # Add category relationship
        self.venue.categories.add(self.category)

        # Create service
        self.service = Service.objects.create(
            venue=self.venue,
            service_title='Massage Therapy',
            short_description='Relaxing massage',
            price_min=Decimal('100.00'),
            price_max=Decimal('200.00'),
            duration_minutes=60,
            is_active=True
        )

        # Admin user for approval
        self.admin_user = User.objects.create_user(
            email='<EMAIL>',
            password='adminpass123'
        )
        self.admin_user.role = User.ADMIN
        self.admin_user.save()

        # Base service discount data
        self.service_discount_data = {
            'service': self.service,
            'name': 'Massage Special',
            'description': 'Special discount for massage therapy',
            'discount_type': DiscountType.PERCENTAGE,
            'discount_value': Decimal('25.00'),
            'start_date': timezone.now() + timedelta(hours=1),
            'end_date': timezone.now() + timedelta(days=7),
            'created_by': self.user
        }

    def test_create_service_discount(self):
        """Test creating a service discount."""
        service_discount = ServiceDiscount.objects.create(**self.service_discount_data)

        self.assertEqual(service_discount.service, self.service)
        self.assertEqual(service_discount.name, 'Massage Special')
        self.assertEqual(service_discount.description, 'Special discount for massage therapy')
        self.assertEqual(service_discount.discount_type, DiscountType.PERCENTAGE)
        self.assertEqual(service_discount.discount_value, Decimal('25.00'))
        self.assertEqual(service_discount.created_by, self.user)
        self.assertFalse(service_discount.is_approved)
        self.assertIsNone(service_discount.approved_by)
        self.assertIsNone(service_discount.approved_at)

    def test_service_discount_string_representation(self):
        """Test service discount string representation."""
        service_discount = ServiceDiscount.objects.create(**self.service_discount_data)
        self.assertEqual(str(service_discount), 'Massage Special')

    def test_service_discount_meta_ordering(self):
        """Test service discount ordering by created_at descending."""
        # Create two discounts with different creation times
        service_discount1 = ServiceDiscount.objects.create(
            name='First Discount',
            **{k: v for k, v in self.service_discount_data.items() if k != 'name'}
        )
        service_discount2 = ServiceDiscount.objects.create(
            name='Second Discount',
            **{k: v for k, v in self.service_discount_data.items() if k != 'name'}
        )

        discounts = list(ServiceDiscount.objects.all())
        self.assertEqual(discounts[0], service_discount2)  # Most recent first
        self.assertEqual(discounts[1], service_discount1)

    def test_service_discount_clean_valid_ownership(self):
        """Test clean method with valid service ownership."""
        service_discount = ServiceDiscount(**self.service_discount_data)
        # Should not raise ValidationError
        service_discount.clean()

    def test_service_discount_clean_invalid_ownership(self):
        """Test clean method with invalid service ownership."""
        # Create another user and service
        other_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        other_user.role = User.SERVICE_PROVIDER
        other_user.save()

        other_provider_profile = ServiceProviderProfile.objects.create(
            user=other_user,
            legal_name='Other Spa',
            phone='+**********',
            contact_name='Jane Doe',
            address='456 Other St',
            city='Boston',
            state='MA',
            zip_code='02101'
        )

        other_venue = Venue.objects.create(
            service_provider=other_provider_profile,
            venue_name='Other Spa Venue',
            short_description='A relaxing spa venue for all your wellness needs',
            street_number='456',
            street_name='Other St',
            city='Boston',
            county='Suffolk',
            state='MA',
            phone='+**********',
            approval_status=Venue.APPROVED
        )
        # Add category relationship
        other_venue.categories.add(self.category)

        other_service = Service.objects.create(
            venue=other_venue,
            service_title='Other Service',
            short_description='Other service',
            price_min=Decimal('50.00'),
            price_max=Decimal('100.00'),
            duration_minutes=30,
            is_active=True
        )

        # Try to create discount for other service
        service_discount = ServiceDiscount(
            service=other_service,  # Different service
            created_by=self.user,  # But same user
            **{k: v for k, v in self.service_discount_data.items() if k != 'service' and k != 'created_by'}
        )

        with self.assertRaises(ValidationError) as context:
            service_discount.clean()
        self.assertIn('You can only create discounts for services in your own venue', str(context.exception))

    def test_service_discount_save_approval_timestamp(self):
        """Test save method sets approved_at when approved."""
        service_discount = ServiceDiscount.objects.create(**self.service_discount_data)

        # Initially not approved
        self.assertFalse(service_discount.is_approved)
        self.assertIsNone(service_discount.approved_at)
        self.assertIsNone(service_discount.approved_by)

        # Approve the discount
        service_discount.is_approved = True
        service_discount.approved_by = self.admin_user
        service_discount.save()

        # Check approval timestamp is set
        self.assertTrue(service_discount.is_approved)
        self.assertEqual(service_discount.approved_by, self.admin_user)
        self.assertIsNotNone(service_discount.approved_at)

    def test_service_discount_save_unapproval_clears_timestamp(self):
        """Test save method clears approved_at when unapproved."""
        service_discount = ServiceDiscount.objects.create(
            is_approved=True,
            approved_by=self.admin_user,
            **self.service_discount_data
        )
        service_discount.approved_at = timezone.now()
        service_discount.save()

        # Unapprove the discount
        service_discount.is_approved = False
        service_discount.save()

        # Check approval data is cleared
        self.assertFalse(service_discount.is_approved)
        self.assertIsNone(service_discount.approved_by)
        self.assertIsNone(service_discount.approved_at)

    def test_service_discount_is_visible_property_approved_and_active(self):
        """Test is_visible property when discount is approved and active."""
        service_discount = ServiceDiscount.objects.create(
            is_approved=True,
            start_date=timezone.now() - timedelta(hours=1),
            end_date=timezone.now() + timedelta(hours=1),
            **{k: v for k, v in self.service_discount_data.items() if k != 'start_date' and k != 'end_date'}
        )
        self.assertTrue(service_discount.is_visible)

    def test_service_discount_is_visible_property_not_approved(self):
        """Test is_visible property when discount is not approved."""
        service_discount = ServiceDiscount.objects.create(
            is_approved=False,
            start_date=timezone.now() - timedelta(hours=1),
            end_date=timezone.now() + timedelta(hours=1),
            **{k: v for k, v in self.service_discount_data.items() if k != 'start_date' and k != 'end_date'}
        )
        self.assertFalse(service_discount.is_visible)

    def test_service_discount_is_visible_property_not_active(self):
        """Test is_visible property when discount is approved but not active."""
        service_discount = ServiceDiscount.objects.create(
            is_approved=True,
            start_date=timezone.now() + timedelta(hours=1),
            end_date=timezone.now() + timedelta(days=1),
            **{k: v for k, v in self.service_discount_data.items() if k != 'start_date' and k != 'end_date'}
        )
        self.assertFalse(service_discount.is_visible)

    def test_service_discount_get_discounted_service_price(self):
        """Test get_discounted_service_price method."""
        service_discount = ServiceDiscount.objects.create(
            discount_type=DiscountType.PERCENTAGE,
            discount_value=Decimal('20.00'),
            **{k: v for k, v in self.service_discount_data.items() if k != 'discount_type' and k != 'discount_value'}
        )

        # Service price_min is 100.00, 20% discount = 80.00
        discounted_price = service_discount.get_discounted_service_price()
        self.assertEqual(discounted_price, Decimal('80.00'))


class PlatformDiscountModelTest(TestCase):
    """Test the PlatformDiscount model functionality."""

    def setUp(self):
        """Set up test data."""
        self.admin_user = User.objects.create_user(
            email='<EMAIL>',
            password='adminpass123'
        )
        self.admin_user.role = User.ADMIN
        self.admin_user.save()

        # Create category
        self.category = Category.objects.create(
            category_name='Spa Services',
            category_description='Relaxing spa treatments',
            is_active=True
        )

        # Base platform discount data
        self.platform_discount_data = {
            'name': 'Summer Sale',
            'description': 'Platform-wide summer discount',
            'discount_type': DiscountType.PERCENTAGE,
            'discount_value': Decimal('15.00'),
            'start_date': timezone.now() + timedelta(hours=1),
            'end_date': timezone.now() + timedelta(days=30),
            'category': self.category,
            'min_booking_value': Decimal('75.00'),
            'max_discount_amount': Decimal('50.00'),
            'is_featured': True,
            'created_by': self.admin_user
        }

    def test_create_platform_discount(self):
        """Test creating a platform discount."""
        platform_discount = PlatformDiscount.objects.create(**self.platform_discount_data)

        self.assertEqual(platform_discount.name, 'Summer Sale')
        self.assertEqual(platform_discount.description, 'Platform-wide summer discount')
        self.assertEqual(platform_discount.discount_type, DiscountType.PERCENTAGE)
        self.assertEqual(platform_discount.discount_value, Decimal('15.00'))
        self.assertEqual(platform_discount.category, self.category)
        self.assertEqual(platform_discount.min_booking_value, Decimal('75.00'))
        self.assertEqual(platform_discount.max_discount_amount, Decimal('50.00'))
        self.assertTrue(platform_discount.is_featured)
        self.assertEqual(platform_discount.created_by, self.admin_user)

    def test_platform_discount_string_representation(self):
        """Test platform discount string representation."""
        platform_discount = PlatformDiscount.objects.create(**self.platform_discount_data)
        self.assertEqual(str(platform_discount), 'Summer Sale')

    def test_platform_discount_meta_ordering(self):
        """Test platform discount ordering by created_at descending."""
        # Create two discounts with different creation times
        platform_discount1 = PlatformDiscount.objects.create(
            name='First Discount',
            **{k: v for k, v in self.platform_discount_data.items() if k != 'name'}
        )
        platform_discount2 = PlatformDiscount.objects.create(
            name='Second Discount',
            **{k: v for k, v in self.platform_discount_data.items() if k != 'name'}
        )

        discounts = list(PlatformDiscount.objects.all())
        self.assertEqual(discounts[0], platform_discount2)  # Most recent first
        self.assertEqual(discounts[1], platform_discount1)

    def test_platform_discount_is_visible_property_active(self):
        """Test is_visible property when discount is active."""
        platform_discount = PlatformDiscount.objects.create(
            start_date=timezone.now() - timedelta(hours=1),
            end_date=timezone.now() + timedelta(hours=1),
            **{k: v for k, v in self.platform_discount_data.items() if k != 'start_date' and k != 'end_date'}
        )
        self.assertTrue(platform_discount.is_visible)

    def test_platform_discount_is_visible_property_not_active(self):
        """Test is_visible property when discount is not active."""
        platform_discount = PlatformDiscount.objects.create(
            start_date=timezone.now() + timedelta(hours=1),
            end_date=timezone.now() + timedelta(days=1),
            **{k: v for k, v in self.platform_discount_data.items() if k != 'start_date' and k != 'end_date'}
        )
        self.assertFalse(platform_discount.is_visible)

    def test_platform_discount_category_optional(self):
        """Test category is optional for platform discounts."""
        platform_discount_data = {k: v for k, v in self.platform_discount_data.items() if k != 'category'}
        platform_discount = PlatformDiscount.objects.create(**platform_discount_data)
        self.assertIsNone(platform_discount.category)

    def test_platform_discount_min_booking_value_default(self):
        """Test min_booking_value defaults to 0.00."""
        platform_discount_data = {k: v for k, v in self.platform_discount_data.items() if k != 'min_booking_value'}
        platform_discount = PlatformDiscount.objects.create(**platform_discount_data)
        self.assertEqual(platform_discount.min_booking_value, Decimal('0.00'))

    def test_platform_discount_max_discount_amount_optional(self):
        """Test max_discount_amount is optional."""
        platform_discount_data = {k: v for k, v in self.platform_discount_data.items() if k != 'max_discount_amount'}
        platform_discount = PlatformDiscount.objects.create(**platform_discount_data)
        self.assertIsNone(platform_discount.max_discount_amount)

    def test_platform_discount_is_featured_default(self):
        """Test is_featured defaults to False."""
        platform_discount_data = {k: v for k, v in self.platform_discount_data.items() if k != 'is_featured'}
        platform_discount = PlatformDiscount.objects.create(**platform_discount_data)
        self.assertFalse(platform_discount.is_featured)


class DiscountUsageModelTest(TestCase):
    """Test the DiscountUsage model functionality."""

    def setUp(self):
        """Set up test data."""
        self.customer_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.customer_user.role = User.CUSTOMER
        self.customer_user.save()

        self.provider_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.provider_user.role = User.SERVICE_PROVIDER
        self.provider_user.save()

        # Create category
        self.category = Category.objects.create(
            category_name='Spa Services',
            category_description='Relaxing spa treatments',
            is_active=True
        )

        # Create service provider profile
        self.provider_profile = ServiceProviderProfile.objects.create(
            user=self.provider_user,
            legal_name='Test Spa',
            phone='+**********',
            contact_name='John Doe',
            address='123 Business St',
            city='New York',
            state='NY',
            zip_code='10001'
        )

        # Create venue
        self.venue = Venue.objects.create(
            service_provider=self.provider_profile,
            venue_name='Test Spa Venue',
            short_description='A relaxing spa venue for all your wellness needs',
            street_number='123',
            street_name='Spa St',
            city='New York',
            county='Manhattan',
            state='NY',
            phone='+**********',
            approval_status=Venue.APPROVED
        )
        # Add category relationship
        self.venue.categories.add(self.category)

        # Create service
        self.service = Service.objects.create(
            venue=self.venue,
            service_title='Massage Therapy',
            short_description='Relaxing massage',
            price_min=Decimal('100.00'),
            price_max=Decimal('200.00'),
            duration_minutes=60,
            is_active=True
        )

        # Create service discount
        self.service_discount = ServiceDiscount.objects.create(
            service=self.service,
            name='Massage Special',
            description='Special discount for massage therapy',
            discount_type=DiscountType.PERCENTAGE,
            discount_value=Decimal('20.00'),
            start_date=timezone.now() - timedelta(hours=1),
            end_date=timezone.now() + timedelta(days=7),
            created_by=self.provider_user,
            is_approved=True
        )

        # Base discount usage data
        self.usage_data = {
            'user': self.customer_user,
            'discount_type': 'ServiceDiscount',
            'discount_id': self.service_discount.id,
            'booking_reference': 'BOOK-12345',
            'original_price': Decimal('100.00'),
            'discount_amount': Decimal('20.00'),
            'final_price': Decimal('80.00')
        }

    def test_create_discount_usage(self):
        """Test creating a discount usage record."""
        usage = DiscountUsage.objects.create(**self.usage_data)

        self.assertEqual(usage.user, self.customer_user)
        self.assertEqual(usage.discount_type, 'ServiceDiscount')
        self.assertEqual(usage.discount_id, self.service_discount.id)
        self.assertEqual(usage.booking_reference, 'BOOK-12345')
        self.assertEqual(usage.original_price, Decimal('100.00'))
        self.assertEqual(usage.discount_amount, Decimal('20.00'))
        self.assertEqual(usage.final_price, Decimal('80.00'))
        self.assertIsNotNone(usage.used_at)

    def test_discount_usage_string_representation(self):
        """Test discount usage string representation."""
        usage = DiscountUsage.objects.create(**self.usage_data)
        expected_str = f"{self.customer_user.email} - ServiceDiscount - {usage.used_at.strftime('%Y-%m-%d')}"
        self.assertEqual(str(usage), expected_str)

    def test_discount_usage_meta_ordering(self):
        """Test discount usage ordering by used_at descending."""
        # Create two usage records with different timestamps
        usage1 = DiscountUsage.objects.create(
            booking_reference='BOOK-11111',
            **{k: v for k, v in self.usage_data.items() if k != 'booking_reference'}
        )
        usage2 = DiscountUsage.objects.create(
            booking_reference='BOOK-22222',
            **{k: v for k, v in self.usage_data.items() if k != 'booking_reference'}
        )

        usages = list(DiscountUsage.objects.all())
        self.assertEqual(usages[0], usage2)  # Most recent first
        self.assertEqual(usages[1], usage1)

    def test_discount_usage_get_discount_object_service_discount(self):
        """Test get_discount_object method for service discount."""
        usage = DiscountUsage.objects.create(**self.usage_data)
        discount_object = usage.get_discount_object()
        self.assertEqual(discount_object, self.service_discount)

    def test_discount_usage_get_discount_object_invalid_type(self):
        """Test get_discount_object method with invalid discount type."""
        usage = DiscountUsage.objects.create(
            discount_type='InvalidType',
            **{k: v for k, v in self.usage_data.items() if k != 'discount_type'}
        )
        discount_object = usage.get_discount_object()
        self.assertIsNone(discount_object)

    def test_discount_usage_get_discount_object_nonexistent_id(self):
        """Test get_discount_object method with nonexistent discount ID."""
        usage = DiscountUsage.objects.create(
            discount_id=99999,  # Nonexistent ID
            **{k: v for k, v in self.usage_data.items() if k != 'discount_id'}
        )
        discount_object = usage.get_discount_object()
        self.assertIsNone(discount_object)

    def test_discount_usage_get_savings_amount(self):
        """Test get_savings_amount method."""
        usage = DiscountUsage.objects.create(**self.usage_data)
        savings = usage.get_savings_amount()
        self.assertEqual(savings, Decimal('20.00'))  # 100.00 - 80.00

    def test_discount_usage_get_savings_percentage(self):
        """Test get_savings_percentage method."""
        usage = DiscountUsage.objects.create(**self.usage_data)
        savings_percentage = usage.get_savings_percentage()
        self.assertEqual(savings_percentage, 20.0)  # 20/100 * 100

    def test_discount_usage_get_savings_percentage_zero_original_price(self):
        """Test get_savings_percentage method with zero original price."""
        usage = DiscountUsage.objects.create(
            original_price=Decimal('0.00'),
            **{k: v for k, v in self.usage_data.items() if k != 'original_price'}
        )
        savings_percentage = usage.get_savings_percentage()
        self.assertEqual(savings_percentage, 0)

    def test_discount_usage_model_choices(self):
        """Test discount model choices are properly defined."""
        expected_choices = [
            ('VenueDiscount', 'Venue Discount'),
            ('ServiceDiscount', 'Service Discount'),
            ('PlatformDiscount', 'Platform Discount'),
        ]
        self.assertEqual(DiscountUsage.DISCOUNT_MODEL_CHOICES, expected_choices)

    def test_discount_usage_indexes(self):
        """Test that database indexes are properly defined."""
        meta = DiscountUsage._meta
        index_fields = [index.fields for index in meta.indexes]

        # Check that expected indexes exist
        self.assertIn(['discount_type', 'discount_id'], index_fields)
        self.assertIn(['user', '-used_at'], index_fields)
        self.assertIn(['-used_at'], index_fields)
