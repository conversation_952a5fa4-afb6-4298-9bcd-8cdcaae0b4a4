# --- Development Settings ---
from .base import *
import sys
from decouple import config

# --- Debug Mode ---
DEBUG = config("DEBUG", default=True, cast=bool)
ENABLE_TEST_VIEW = DEBUG

# --- Allowed Hosts ---
ALLOWED_HOSTS = ['localhost', '127.0.0.1', 'testserver']

# --- CSRF Configuration ---
CSRF_TRUSTED_ORIGINS = [
    'http://localhost:8000',
    'http://127.0.0.1:8000',
]

# --- Email Configuration for Development ---
# Use console backend to print emails to console instead of sending them
EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'

# Alternative: Configure SMTP for real email testing
# To enable real email sending, set FORCE_EMAIL_BACKEND=True in your .env file
# and uncomment the following lines:
FORCE_EMAIL_BACKEND = config('FORCE_EMAIL_BACKEND', default=False, cast=bool)

if FORCE_EMAIL_BACKEND:
    EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
    EMAIL_HOST = config('EMAIL_HOST', default='smtp.sendgrid.net')
    EMAIL_PORT = config('EMAIL_PORT', default=587, cast=int)
    EMAIL_USE_TLS = config('EMAIL_USE_TLS', default=True, cast=bool)
    EMAIL_HOST_USER = config('EMAIL_HOST_USER', default='apikey')
    EMAIL_HOST_PASSWORD = config('EMAIL_HOST_PASSWORD', default='')
    EMAIL_TIMEOUT = config('EMAIL_TIMEOUT', default=30, cast=int)
    DEFAULT_FROM_EMAIL = config('DEFAULT_FROM_EMAIL', default='<EMAIL>')
    SERVER_EMAIL = config('SERVER_EMAIL', default='<EMAIL>')

# --- Context Processors (Minimal for navbar) ---
# Only add basic context processors that don't require additional apps 