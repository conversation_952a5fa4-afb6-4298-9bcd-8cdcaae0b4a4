# Manual Testing Checklist - Accounts App

## Customer Registration & Authentication
1. Customer can register with valid email and password
2. Customer receives welcome email after registration
3. Customer can login and logout with valid credentials
4. Customer receives error for invalid login credentials
5. Customer session expires after configured inactivity period
6. Customer account lockout works after multiple failed login attempts

## Customer Profile Management
7. Customer can view their complete profile information 
8. Customer can edit profile details (name, phone, address, birth month, gender)
9. Customer can upload and update profile picture
10. Customer profile completion percentage displays correctly
11. Customer can change password from dashboard
12. Customer is logged out automatically after password change
13. Customer can deactivate their account successfully

## Service Provider Registration & Verification
14. Service provider can register with business information and contact details
15. Service provider receives email verification after registration
16. Service provider account remains inactive until email verification
17. Service provider can verify email using verification link
18. Service provider can resend verification email if needed
19. Service provider cannot login before email verification

## Service Provider Authentication & Profile
20. Service provider can login after email verification
21. Service provider can logout successfully
22. Service provider can view and edit business profile information
23. Service provider can update legal name, display name, and description
24. Service provider can upload business logo and update contact information
25. Service provider can add social media links and website URL
26. Service provider can toggle public visibility settings
27. Service provider can change password from dashboard
28. Service provider can deactivate business account

## Password Management (All Users)
29. Users can request password reset via email
30. Password reset email contains valid working token
31. Users can reset password using valid token within expiry time
32. Password reset fails with expired or invalid tokens
33. Strong password requirements are enforced during reset
34. Password history prevents reuse of previous passwords

## Admin User Management
35. Admin can view all users with role and status filters
36. Admin can search users by email and name
37. Admin can activate and deactivate user accounts in bulk
38. Admin can send password reset emails to users
39. Admin can view detailed user profile information

## Admin Security Monitoring
40. Admin can view login history for all users
41. Admin can see failed login attempts and suspicious patterns
42. Admin can view and resolve login alerts
43. Admin can manually clear account lockouts
44. Admin can export security and login history reports

## Email System Functionality
45. Email verification links work correctly and expire after 24 hours
46. Password reset emails are sent with proper formatting
47. Welcome emails are sent to customers after registration
48. Email verification for service providers works as expected

## Security & Account Lockout
49. Account locks after configured number of failed attempts
50. Account lockout displays appropriate error messages
51. Locked accounts unlock automatically after timeout period
52. IP-based lockout works for repeated failed attempts
53. Admin can manually unlock locked accounts

## Session & Access Control
54. Role-based access control works (customers/providers/admin areas)
55. Unauthorized access redirects to appropriate login page
56. Session management works correctly across browser tabs
57. Users can access unified logout from any location

## Privacy & Data Management
58. Users can manage privacy settings and profile visibility
59. Users can control email preferences and notifications
60. Users can request data export (GDPR compliance)
61. Data export includes complete user information in proper format
62. Users can request account deletion

## AJAX & Real-time Features
63. Profile updates work without page refresh
64. Real-time field validation works during form input
65. Profile image upload works via AJAX
66. Error handling works properly for AJAX requests

## Integration & Cross-App Functionality
67. Authentication works consistently across all apps
68. User roles are respected throughout the system
69. Profile completion suggestions work correctly
70. Email templates render properly in all contexts

## Error Handling & Validation
71. Form validation shows user-friendly error messages
72. Server-side validation works for all forms
73. Database errors are handled gracefully without exposing sensitive data
74. Network timeouts and errors show appropriate messages

## Production Readiness
75. All email functionality works in production environment
76. SSL/TLS security works for authentication pages
77. Performance is acceptable under normal load
78. Logging captures security events properly
79. Error monitoring works without exposing sensitive information
80. Backup and recovery procedures work for user data

---
Notes:
- Test each feature in both development and production environments
- Verify all error messages are user-friendly and don't expose sensitive data
- Check that all security features work as expected
- Ensure GDPR compliance features are functioning properly
- Test cross-browser compatibility for critical authentication flows 
