# CozyWish Environment Variables
# Copy this file to .env and fill in your actual values
# Use `make envfile` to automatically copy this file to .env

# ============================================================================
# CORE DJANGO SETTINGS
# ============================================================================

# Enable/disable Django debug mode (affects error pages, logging, etc.)
DEBUG=True

# Django secret key for cryptographic signing (CRITICAL: Change in production!)
SECRET_KEY=your-super-secret-key-here-change-in-production

# Django settings module to load (determines environment behavior)
DJANGO_SETTINGS_MODULE=project_root.settings.development

# Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO

# ============================================================================
# DATABASE CONFIGURATION
# ============================================================================

# Database connection URL (SQLite for development, PostgreSQL for production)
DATABASE_URL=sqlite:///db.sqlite3
# PostgreSQL example: DATABASE_URL=postgresql://user:password@localhost:5432/cozywish
# PostgreSQL with SSL: DATABASE_URL=************************************/db?sslmode=require

# ============================================================================
# SITE CONFIGURATION
# ============================================================================

# Base URL of the site (used for absolute URLs in emails, etc.)
SITE_URL=https://localhost:8000

# Comma-separated list of allowed hosts (domains that can serve the site)
ALLOWED_HOSTS=localhost,127.0.0.1
# Production example: ALLOWED_HOSTS=.cozywish.com,cozywish.onrender.com

# Comma-separated list of trusted CSRF origins (for cross-origin requests)
CSRF_TRUSTED_ORIGINS=https://localhost:8000
# Production example: CSRF_TRUSTED_ORIGINS=https://www.cozywish.com,https://cozywish.onrender.com

# ============================================================================
# EMAIL CONFIGURATION (SendGrid)
# ============================================================================

# SMTP server settings
EMAIL_HOST=smtp.sendgrid.net
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=apikey
EMAIL_TIMEOUT=30

# SendGrid API key (REQUIRED for staging/production)
EMAIL_HOST_PASSWORD=your-sendgrid-api-key-here

# Email addresses used by the system
DEFAULT_FROM_EMAIL=<EMAIL>
SERVER_EMAIL=<EMAIL>

# Force SMTP backend in development (normally uses console backend)
FORCE_EMAIL_BACKEND=False

# ============================================================================
# SUPPORT CONTACT EMAILS
# ============================================================================

# Various support and contact email addresses
SUPPORT_EMAIL=<EMAIL>
CONTACT_EMAIL=<EMAIL>
BUSINESS_EMAIL=<EMAIL>
PRIVACY_EMAIL=<EMAIL>
PRESS_EMAIL=<EMAIL>

# ============================================================================
# SECURITY EMAIL ADDRESSES
# ============================================================================

# Recipients for security-related notifications and alerts
SECURITY_NOTIFICATION_EMAIL=<EMAIL>
SECURITY_ALERT_EMAIL=<EMAIL>

# ============================================================================
# AWS S3 CONFIGURATION
# ============================================================================

# AWS credentials for S3 storage (REQUIRED for production media files)
AWS_ACCESS_KEY_ID=your-aws-access-key-here
AWS_SECRET_ACCESS_KEY=your-aws-secret-key-here
AWS_STORAGE_BUCKET_NAME=your-s3-bucket-name
AWS_S3_REGION_NAME=us-east-1

# Optional: Custom domain for S3 (e.g., CDN domain)
AWS_S3_CUSTOM_DOMAIN=your-custom-domain.com

# ============================================================================
# CACHE CONFIGURATION
# ============================================================================

# Cache timeout settings (in seconds)
DASHBOARD_CACHE_TIMEOUT=300
NOTIFICATION_CACHE_TIMEOUT=60

# ============================================================================
# BUSINESS CONFIGURATION
# ============================================================================

# Platform fee rate as decimal (e.g., 0.05 = 5%)
PLATFORM_FEE_RATE=0.05

# ============================================================================
# CELERY CONFIGURATION
# ============================================================================

# Redis URLs for Celery message broker and result backend
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# ============================================================================
# THIRD-PARTY API KEYS
# ============================================================================

# Stripe payment processing keys
STRIPE_PUBLIC_KEY=pk_test_your-stripe-publishable-key
STRIPE_SECRET_KEY=sk_test_your-stripe-secret-key
STRIPE_WEBHOOK_SECRET=whsec_your-stripe-webhook-secret

# ============================================================================
# OAUTH CONFIGURATION
# ============================================================================

# Google OAuth credentials for social authentication
GOOGLE_OAUTH_CLIENT_ID=your-google-oauth-client-id
GOOGLE_OAUTH_CLIENT_SECRET=your-google-oauth-client-secret

# ============================================================================
# DEVELOPMENT SETTINGS
# ============================================================================

# Enable Django debug toolbar in development
DJANGO_DEBUG_TOOLBAR=True

# Enable test views and endpoints
ENABLE_TEST_VIEW=True

# ============================================================================
# DEPLOYMENT SETTINGS
# ============================================================================

# Render.com specific settings
RENDER_EXTERNAL_HOSTNAME=your-app.onrender.com
RENDER=false

# ============================================================================
# SECURITY SETTINGS
# ============================================================================

# Skip settings validation (use with caution)
SKIP_SETTINGS_VALIDATION=False

# Enable settings validation during tests
ENABLE_SETTINGS_VALIDATION_IN_TESTS=False

# ============================================================================
# ADMIN CONFIGURATION
# ============================================================================

# Default admin user credentials (change these!)
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=secure-admin-password-here

# ============================================================================
# ENVIRONMENT-SPECIFIC NOTES
# ============================================================================

# DEVELOPMENT:
# - Most settings have sensible defaults
# - EMAIL_HOST_PASSWORD is optional (uses console backend)
# - DEBUG=True enables debug mode
# - Uses SQLite database by default

# STAGING:
# - EMAIL_HOST_PASSWORD is required
# - DATABASE_URL should point to PostgreSQL
# - DEBUG=False (production-like behavior)
# - AWS credentials optional (falls back to local storage)

# PRODUCTION:
# - All staging requirements plus:
# - AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY, AWS_STORAGE_BUCKET_NAME are required
# - ALLOWED_HOSTS and CSRF_TRUSTED_ORIGINS must be set correctly
# - Use strong SECRET_KEY value
# - DEBUG=False (security requirement)

# ============================================================================
# QUICK SETUP COMMANDS
# ============================================================================

# 1. Copy this file:           cp env.example .env  OR  make envfile
# 2. Edit with your values:    nano .env
# 3. Run migrations:           python manage.py migrate
# 4. Create superuser:         python manage.py createsuperuser
# 5. Start development:        python manage.py runserver 