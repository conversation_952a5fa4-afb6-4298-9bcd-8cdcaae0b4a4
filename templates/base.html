{% load static %}
<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{% block title %}CozyWish{% endblock %}</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Brand Styles -->
    <link rel="stylesheet" href="{% static 'css/cozywish_main.css' %}">
    <link rel="stylesheet" href="{% static 'css/cozywish_navbar.css' %}">
    <link rel="stylesheet" href="{% static 'css/auto_dismiss_messages.css' %}">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" crossorigin="anonymous" />

    {% block extra_css %}{% endblock %}
</head>
<body class="{% block body_class %}{% endblock %}">

<header>
    {% include 'includes/navbar.html' %}
</header>


<main>
    <!-- Global messages container -->
    {% if messages %}
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="messages-container" role="alert" aria-live="polite">
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                <i class="fas fa-{% if message.tags == 'error' %}exclamation-triangle{% elif message.tags == 'success' %}check-circle{% elif message.tags == 'warning' %}exclamation-triangle{% else %}info-circle{% endif %} me-2"></i>
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    {% endif %}

    {% block content %}{% endblock %}
</main>




<footer>
    {% block footer_content %}{% endblock %}
</footer>

<!-- Bootstrap Bundle JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

<!-- Auto-dismiss messages system -->
<script src="{% static 'js/auto_dismiss_messages.js' %}"></script>

{% block extra_js %}{% endblock %}
</body>
</html> 