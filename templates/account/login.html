{% extends 'base.html' %}
{% load widget_tweaks %}
{% load allauth %}

{% block title %}Login - CozyWish{% endblock %}

{% block extra_css %}
<!-- Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

<style>
/* Login Page Styles */
.login-section {
    min-height: 100vh;
    background: linear-gradient(135deg, #FFF9F4 0%, #FAE1D7 100%);
    padding: 2rem 0;
    display: flex;
    align-items: center;
}

.login-container {
    max-width: 500px;
    margin: 0 auto;
    padding: 0 1rem;
}

.login-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(66, 36, 26, 0.1);
    overflow: hidden;
    border: 2px solid #FAE1D7;
}

.login-header {
    background: linear-gradient(135deg, #42241A 0%, #2F160F 100%);
    color: white;
    padding: 2rem;
    text-align: center;
}

.login-icon {
    width: 60px;
    height: 60px;
    background: rgba(250, 225, 215, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    font-size: 1.5rem;
}

.login-title {
    font-family: 'Poppins', sans-serif;
    font-size: 1.75rem;
    font-weight: 700;
    margin: 0 0 0.5rem;
}

.login-subtitle {
    font-family: 'Inter', sans-serif;
    font-size: 1rem;
    opacity: 0.9;
    margin: 0;
}

.login-body {
    padding: 2rem;
}

.form-control-cw {
    border: 2px solid #E0DFDE;
    border-radius: 12px;
    padding: 0.875rem 1rem;
    font-family: 'Inter', sans-serif;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #FEFEFE;
}

.form-control-cw:focus {
    border-color: #42241A;
    box-shadow: 0 0 0 0.2rem rgba(66, 36, 26, 0.1);
    outline: none;
}

.form-label {
    font-family: 'Inter', sans-serif;
    font-weight: 600;
    color: #2F160F;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-cw-primary {
    background: linear-gradient(135deg, #42241A 0%, #2F160F 100%);
    border: none;
    color: white;
    padding: 0.875rem 2rem;
    border-radius: 12px;
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.btn-cw-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(66, 36, 26, 0.3);
    color: white;
}

.btn-cw-secondary {
    background: white;
    border: 2px solid #42241A;
    color: #42241A;
    padding: 0.875rem 2rem;
    border-radius: 12px;
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.btn-cw-secondary:hover {
    background: #42241A;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(66, 36, 26, 0.2);
}

.alert-cw {
    border-radius: 12px;
    border: none;
    padding: 1rem 1.25rem;
    font-family: 'Inter', sans-serif;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.alert-cw-error {
    background: #FEF2F2;
    color: #DC2626;
    border-left: 4px solid #DC2626;
}

.invalid-feedback {
    color: #DC2626;
    font-family: 'Inter', sans-serif;
    font-size: 0.875rem;
    margin-top: 0.25rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.text-center a {
    color: #42241A;
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.text-center a:hover {
    color: #2F160F;
    text-decoration: underline;
}

@media (max-width: 576px) {
    .login-container {
        padding: 0 1rem;
    }
    
    .login-header {
        padding: 1.5rem;
    }
    
    .login-body {
        padding: 1.5rem;
    }
}
</style>
{% endblock %}

{% block content %}
<section class="login-section">
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <div class="login-icon">
                    <i class="fas fa-sign-in-alt"></i>
                </div>
                <h1 class="login-title">Welcome Back</h1>
                <p class="login-subtitle">Sign in to your CozyWish account</p>
            </div>
            
            <div class="login-body">
                <form method="post" novalidate>
                    {% csrf_token %}
                    
                    {% if form.non_field_errors %}
                    <div class="alert-cw alert-cw-error mb-4">
                        {% for error in form.non_field_errors %}
                        <i class="fas fa-exclamation-circle"></i>{{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}
                    
                    <!-- Email Field -->
                    <div class="mb-3">
                        <label for="{{ form.login.id_for_label }}" class="form-label">
                            <i class="fas fa-envelope"></i>{{ form.login.label }}
                        </label>
                        {% if form.login.errors %}
                            {{ form.login|add_class:"form-control-cw is-invalid" }}
                        {% else %}
                            {{ form.login|add_class:"form-control-cw" }}
                        {% endif %}
                        {% if form.login.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.login.errors %}
                            <i class="fas fa-exclamation-circle"></i>{{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                    
                    <!-- Password Field -->
                    <div class="mb-4">
                        <label for="{{ form.password.id_for_label }}" class="form-label">
                            <i class="fas fa-lock"></i>{{ form.password.label }}
                        </label>
                        <div class="input-group">
                            {% if form.password.errors %}
                                {{ form.password|add_class:"form-control-cw is-invalid" }}
                            {% else %}
                                {{ form.password|add_class:"form-control-cw" }}
                            {% endif %}
                            <button type="button" class="btn toggle-password" data-target="#{{ form.password.id_for_label }}" title="Show password" aria-label="Toggle password visibility">
                                <i class="fas fa-eye" aria-hidden="true"></i>
                            </button>
                        </div>
                        {% if form.password.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.password.errors %}
                            <i class="fas fa-exclamation-circle"></i>{{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                    
                    <!-- Remember Me -->
                    {% if form.remember %}
                    <div class="mb-4">
                        <div class="form-check">
                            {{ form.remember|add_class:"form-check-input" }}
                            <label class="form-check-label" for="{{ form.remember.id_for_label }}">
                                {{ form.remember.label }}
                            </label>
                        </div>
                    </div>
                    {% endif %}
                    
                    <!-- Submit Button -->
                    <div class="d-grid mb-4">
                        <button type="submit" class="btn-cw-primary">
                            <i class="fas fa-sign-in-alt"></i>Sign In
                        </button>
                    </div>
                    
                    <!-- Links -->
                    <div class="text-center">
                        <p class="mb-2">
                            <a href="{% url 'account_reset_password' %}">Forgot your password?</a>
                        </p>
                        <p class="mb-0">
                            Don't have an account? 
                            <a href="{% url 'account_signup' %}">Sign up here</a>
                        </p>
                    </div>
                </form>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<!-- Password Toggle JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    'use strict';

    // Password toggle functionality
    function togglePassword(toggleBtn) {
        const targetSelector = toggleBtn.getAttribute('data-target');
        if (!targetSelector) {
            console.error('No data-target attribute found on toggle button');
            return;
        }

        const input = document.querySelector(targetSelector);
        if (!input) {
            console.error('Target input not found:', targetSelector);
            return;
        }

        // Toggle password visibility
        const isPassword = input.type === 'password';
        input.type = isPassword ? 'text' : 'password';

        // Update icon
        const icon = toggleBtn.querySelector('i');
        if (icon) {
            if (isPassword) {
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        }

        // Update accessibility attributes
        const newTitle = isPassword ? 'Hide password' : 'Show password';
        toggleBtn.title = newTitle;
        toggleBtn.setAttribute('aria-label', newTitle);
    }

    // Click event handler
    document.addEventListener('click', function(e) {
        const toggleBtn = e.target.closest('.toggle-password');
        if (toggleBtn) {
            e.preventDefault();
            e.stopPropagation();
            togglePassword(toggleBtn);
        }
    });

    // Keyboard event handler for accessibility
    document.addEventListener('keydown', function(e) {
        const toggleBtn = e.target.closest('.toggle-password');
        if (toggleBtn && (e.key === 'Enter' || e.key === ' ' || e.key === 'Spacebar')) {
            e.preventDefault();
            e.stopPropagation();
            togglePassword(toggleBtn);
        }
    });

    // Initialize toggle buttons
    const toggleButtons = document.querySelectorAll('.toggle-password');
    toggleButtons.forEach(function(btn) {
        // Ensure button has proper attributes
        if (!btn.hasAttribute('tabindex')) {
            btn.setAttribute('tabindex', '0');
        }
        
        // Add role for screen readers
        btn.setAttribute('role', 'button');
        
        // Ensure aria-label is set
        if (!btn.hasAttribute('aria-label')) {
            btn.setAttribute('aria-label', 'Toggle password visibility');
        }
    });
});
</script>
{% endblock %}
