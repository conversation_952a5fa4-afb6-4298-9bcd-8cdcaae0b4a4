{% extends 'base.html' %}
{% load widget_tweaks %}

{% block title %}Logout - CozyWish{% endblock %}

{% block extra_css %}
<!-- Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

<style>
/* Logout Page Styles */
.logout-section {
    min-height: 100vh;
    background: linear-gradient(135deg, #FFF9F4 0%, #FAE1D7 100%);
    padding: 2rem 0;
    display: flex;
    align-items: center;
}

.logout-container {
    max-width: 500px;
    margin: 0 auto;
    padding: 0 1rem;
}

.logout-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(66, 36, 26, 0.1);
    overflow: hidden;
    border: 2px solid #FAE1D7;
}

.logout-header {
    background: linear-gradient(135deg, #42241A 0%, #2F160F 100%);
    color: white;
    padding: 2rem;
    text-align: center;
}

.logout-icon {
    width: 60px;
    height: 60px;
    background: rgba(250, 225, 215, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem auto;
    font-size: 1.5rem;
}

.logout-title {
    font-family: 'Playfair Display', serif;
    font-size: 1.75rem;
    font-weight: 600;
    margin: 0 0 0.5rem 0;
}

.logout-subtitle {
    font-family: 'Inter', sans-serif;
    font-size: 1rem;
    opacity: 0.9;
    margin: 0;
    font-weight: 400;
}

.logout-body {
    padding: 2rem;
    text-align: center;
}

.logout-message {
    font-family: 'Inter', sans-serif;
    font-size: 1.1rem;
    color: #42241A;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.form-control-cw {
    border: 2px solid #FAE1D7;
    border-radius: 12px;
    padding: 0.875rem 1rem;
    font-family: 'Inter', sans-serif;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #FEFEFE;
}

.form-control-cw:focus {
    border-color: #42241A;
    box-shadow: 0 0 0 3px rgba(66, 36, 26, 0.1);
    outline: none;
    background: white;
}

.form-label {
    font-family: 'Inter', sans-serif;
    font-weight: 600;
    color: #42241A;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-cw-primary {
    background: linear-gradient(135deg, #42241A 0%, #2F160F 100%);
    border: none;
    color: white;
    padding: 0.875rem 2rem;
    border-radius: 12px;
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.btn-cw-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(66, 36, 26, 0.3);
    color: white;
}

.btn-cw-secondary {
    background: white;
    border: 2px solid #42241A;
    color: #42241A;
    padding: 0.875rem 2rem;
    border-radius: 12px;
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    width: 100%;
}

.btn-cw-secondary:hover {
    background: #42241A;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(66, 36, 26, 0.2);
    text-decoration: none;
}

.alert-cw {
    border-radius: 12px;
    border: none;
    padding: 1rem 1.25rem;
    font-family: 'Inter', sans-serif;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
}

.alert-cw-success {
    background: #F0FDF4;
    color: #16A34A;
    border-left: 4px solid #16A34A;
}

.text-center a {
    color: #42241A;
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.text-center a:hover {
    color: #2F160F;
    text-decoration: underline;
}

@media (max-width: 576px) {
    .logout-container {
        padding: 0 1rem;
    }
    
    .logout-header {
        padding: 1.5rem;
    }
    
    .logout-body {
        padding: 1.5rem;
    }
}
</style>
{% endblock %}

{% block content %}
<section class="logout-section">
    <div class="logout-container">
        <div class="logout-card">
            <div class="logout-header">
                <div class="logout-icon">
                    <i class="fas fa-sign-out-alt"></i>
                </div>
                <h1 class="logout-title">Sign Out</h1>
                <p class="logout-subtitle">Thanks for using CozyWish!</p>
            </div>
            
            <div class="logout-body">
                <p class="logout-message">
                    Are you sure you want to sign out of your account?
                </p>
                
                <form method="post">
                    {% csrf_token %}
                    
                    <!-- Logout Button -->
                    <div class="d-grid mb-3">
                        <button type="submit" class="btn-cw-primary">
                            <i class="fas fa-sign-out-alt"></i>Yes, Sign Me Out
                        </button>
                    </div>
                    
                    <!-- Cancel Button -->
                    <div class="d-grid">
                        <a href="{% if user.is_authenticated %}{% if user.role == 'customer' %}/{% elif user.role == 'service_provider' %}/dashboard/{% else %}/{% endif %}{% else %}/{% endif %}" class="btn-cw-secondary">
                            <i class="fas fa-arrow-left"></i>Cancel
                        </a>
                    </div>
                </form>
                
                <!-- Additional Links -->
                <div class="text-center mt-4">
                    <p class="mb-0">
                        <a href="/">Return to Home Page</a>
                    </p>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %} 