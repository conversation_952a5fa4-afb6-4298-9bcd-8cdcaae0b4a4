{% extends 'accounts_app/base_guest.html' %}

{% block title %}Welcome Back - CozyWish{% endblock %}

{% block extra_css %}
{% load static %}
{% load widget_tweaks %}
<link rel="stylesheet" href="{% static 'css/accounts_app/customer_login.css' %}">

<style>
/* ---- CozyWish Design Reference Overrides ---- */
.login-card {
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
}

.login-header {
    background: transparent;
    border-bottom: none;
}

.login-header i {
    font-size: 2.5rem;
    color: var(--cw-brand-primary, #2F160F);
}

/* Password Toggle Button Styling */
.toggle-password {
    border: 1px solid #ced4da !important;
    border-left: none !important;
    background: white !important;
    color: #6c757d !important;
    padding: 0.375rem 0.75rem !important;
    border-radius: 0 0.25rem 0.25rem 0 !important;
    transition: all 0.2s ease;
    cursor: pointer;
}

.toggle-password:hover,
.toggle-password:focus {
    background: #f8f9fa !important;
    border-color: #86b7fe !important;
    color: #0d6efd !important;
    outline: none;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.input-group .form-control:focus + .toggle-password {
    border-color: #86b7fe !important;
}

.input-group .form-control.is-invalid + .toggle-password {
    border-color: #dc3545 !important;
}
</style>
{% endblock %}

{% block content %}
<div class="card login-card">
    <div class="login-header">
        <i class="fas fa-user-circle"></i>
        <h2>Welcome Back</h2>
        <p class="text-muted">Sign in to your CozyWish account</p>
    </div>
    <div class="card-body px-4">
        <form method="post">
            {% csrf_token %}
            
            {% if form.non_field_errors %}
                <div class="alert alert-danger mb-3">
                    {% for error in form.non_field_errors %}
                        <i class="fas fa-exclamation-circle me-2"></i>{{ error }}
                    {% endfor %}
                </div>
            {% endif %}

            <div class="mb-3">
                <label for="{{ form.login.id_for_label }}" class="form-label">
                    <i class="fas fa-envelope me-2"></i>Email Address
                </label>
                {% if form.login.errors %}
                    {{ form.login|add_class:"form-control is-invalid"|attr:"placeholder:Enter your email address" }}
                    <div class="invalid-feedback">
                        {% for error in form.login.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                {% else %}
                    {{ form.login|add_class:"form-control"|attr:"placeholder:Enter your email address" }}
                {% endif %}
            </div>
            
            <div class="mb-3">
                <label for="{{ form.password.id_for_label }}" class="form-label">
                    <i class="fas fa-lock me-2"></i>Password
                </label>
                <div class="input-group">
                    {% if form.password.errors %}
                        {{ form.password|add_class:"form-control is-invalid"|attr:"placeholder:Enter your password" }}
                    {% else %}
                        {{ form.password|add_class:"form-control"|attr:"placeholder:Enter your password" }}
                    {% endif %}
                    <button type="button" class="btn toggle-password" data-target="#{{ form.password.id_for_label }}" title="Show password" aria-label="Toggle password visibility">
                        <i class="fas fa-eye" aria-hidden="true"></i>
                    </button>
                </div>
                {% if form.password.errors %}
                    <div class="invalid-feedback">
                        {% for error in form.password.errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                {% endif %}
            </div>
            
            <div class="d-flex justify-content-between align-items-center mb-3">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="remember" name="remember">
                    <label class="form-check-label" for="remember">Remember me</label>
                </div>
                <a href="#" class="text-decoration-none small">Forgot password?</a>
            </div>
            
            <button type="submit" class="btn btn-dark w-100 btn-custom mb-3">
                <i class="fas fa-sign-in-alt me-2"></i>Sign In
            </button>
        </form>

        <div class="divider"><small class="text-muted">or continue with</small></div>
        <div class="text-center mb-3">
            <button class="btn btn-outline-secondary rounded-circle me-2"><i class="fab fa-google"></i></button>
            <button class="btn btn-outline-secondary rounded-circle me-2"><i class="fab fa-github"></i></button>
            <button class="btn btn-outline-secondary rounded-circle"><i class="fab fa-facebook"></i></button>
            <p class="small mt-2 text-muted">Social login coming soon</p>
        </div>

        <div class="text-center">
            <p class="mb-1">Don't have an account?</p>
            <a href="{% url 'accounts_app:customer_signup' %}" class="btn btn-outline-dark w-100 mb-2 btn-custom">
                <i class="fas fa-user-plus me-2"></i>Sign up as Customer
            </a>
            <p class="mb-1">Are you a service provider?</p>
            <a href="{% url 'accounts_app:for_business' %}" class="btn btn-outline-dark w-100 btn-custom">
                <i class="fas fa-briefcase me-2"></i>For Business
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    'use strict';

    // Password toggle functionality
    function togglePassword(toggleBtn) {
        const targetSelector = toggleBtn.getAttribute('data-target');
        if (!targetSelector) {
            console.error('No data-target attribute found on toggle button');
            return;
        }

        const input = document.querySelector(targetSelector);
        if (!input) {
            console.error('Target input not found:', targetSelector);
            return;
        }

        // Toggle password visibility
        const isPassword = input.type === 'password';
        input.type = isPassword ? 'text' : 'password';

        // Update icon
        const icon = toggleBtn.querySelector('i');
        if (icon) {
            if (isPassword) {
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        }

        // Update accessibility attributes
        const newTitle = isPassword ? 'Hide password' : 'Show password';
        toggleBtn.title = newTitle;
        toggleBtn.setAttribute('aria-label', newTitle);
    }

    // Click event handler
    document.addEventListener('click', function(e) {
        const toggleBtn = e.target.closest('.toggle-password');
        if (toggleBtn) {
            e.preventDefault();
            e.stopPropagation();
            togglePassword(toggleBtn);
        }
    });

    // Keyboard event handler for accessibility
    document.addEventListener('keydown', function(e) {
        const toggleBtn = e.target.closest('.toggle-password');
        if (toggleBtn && (e.key === 'Enter' || e.key === ' ' || e.key === 'Spacebar')) {
            e.preventDefault();
            e.stopPropagation();
            togglePassword(toggleBtn);
        }
    });

    // Initialize toggle buttons
    const toggleButtons = document.querySelectorAll('.toggle-password');
    toggleButtons.forEach(function(btn) {
        // Ensure button has proper attributes
        if (!btn.hasAttribute('tabindex')) {
            btn.setAttribute('tabindex', '0');
        }
        
        // Add role for screen readers
        btn.setAttribute('role', 'button');
        
        // Ensure aria-label is set
        if (!btn.hasAttribute('aria-label')) {
            btn.setAttribute('aria-label', 'Toggle password visibility');
        }
    });
});
</script>
{% endblock %}
