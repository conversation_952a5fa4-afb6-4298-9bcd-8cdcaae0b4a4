{% extends 'base.html' %}

{% block title %}Email Verification Failed - CozyWish{% endblock %}

{% block extra_css %}
{% load static %}
{% load widget_tweaks %}

<!-- Google Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

<style>
    /* CozyWish Design System - Email Verification Failed */
    :root {
        /* Brand Colors */
        --cw-brand-primary: #2F160F;
        --cw-brand-light: #4a2a1f;
        --cw-brand-accent: #fae1d7;
        --cw-accent-light: #fef7f0;
        --cw-accent-dark: #f1d4c4;

        /* Neutral Colors */
        --cw-neutral-600: #525252;
        --cw-neutral-700: #404040;
        --cw-neutral-800: #262626;

        /* Semantic Colors */
        --cw-error: #dc2626;
        --cw-error-light: #fef2f2;
        --cw-error-border: #fecaca;
        --cw-warning: #d97706;
        --cw-warning-light: #fffbeb;
        --cw-warning-border: #fed7aa;

        /* Typography */
        --cw-font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-heading: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        --cw-font-display: 'Playfair Display', Georgia, 'Times New Roman', serif;

        /* Shadows */
        --cw-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --cw-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --cw-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

        /* Gradients */
        --cw-gradient-hero: radial-gradient(ellipse at center, var(--cw-brand-accent) 40%, var(--cw-accent-light) 70%, #ffffff 100%);
        --cw-gradient-brand-button: linear-gradient(135deg, var(--cw-brand-primary) 0%, var(--cw-brand-light) 100%);
    }

    /* Reset and base styles */
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    body {
        font-family: var(--cw-font-primary);
        line-height: 1.6;
        color: var(--cw-neutral-800);
        background: var(--cw-gradient-hero);
    }

    /* Email Verification Failed Section */
    .email-verify-failed-section {
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 2rem 1rem;
    }

    .email-verify-failed-container {
        width: 100%;
        max-width: 600px;
        margin: 0 auto;
    }

    .email-verify-failed-card {
        background: white;
        border-radius: 1.5rem;
        box-shadow: var(--cw-shadow-lg);
        overflow: hidden;
        border: 2px solid var(--cw-brand-accent);
    }

    .email-verify-failed-header {
        background: linear-gradient(135deg, var(--cw-error) 0%, #ef4444 100%);
        color: white;
        padding: 3rem 2rem 2rem;
        text-align: center;
        position: relative;
    }

    /* Decorative Pattern */
    .email-verify-failed-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="error-pattern" x="0" y="0" width="25" height="25" patternUnits="userSpaceOnUse"><circle cx="12.5" cy="12.5" r="1.5" fill="%23fecaca" opacity="0.4"/></pattern></defs><rect width="100" height="100" fill="url(%23error-pattern)"/></svg>') repeat;
        opacity: 0.6;
        z-index: 1;
    }

    .email-verify-failed-header .content {
        position: relative;
        z-index: 2;
    }

    .email-verify-failed-icon {
        width: 80px;
        height: 80px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 2rem;
        margin-bottom: 1.5rem;
        box-shadow: var(--cw-shadow-md);
    }

    .email-verify-failed-title {
        font-family: var(--cw-font-display);
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 1rem;
        line-height: 1.2;
    }

    .email-verify-failed-subtitle {
        font-size: 1.125rem;
        margin-bottom: 0;
        line-height: 1.6;
        opacity: 0.9;
        max-width: 500px;
        margin-left: auto;
        margin-right: auto;
    }

    .email-verify-failed-body {
        padding: 3rem 2rem;
    }

    /* Alert Styling */
    .alert-cw {
        border-radius: 0.75rem;
        padding: 1rem 1.25rem;
        margin-bottom: 1.5rem;
        border: 1px solid;
        font-family: var(--cw-font-primary);
        display: flex;
        align-items: flex-start;
        gap: 0.75rem;
    }

    .alert-cw-error {
        background-color: var(--cw-error-light);
        border-color: var(--cw-error-border);
        color: var(--cw-error);
    }

    .alert-cw-warning {
        background-color: var(--cw-warning-light);
        border-color: var(--cw-warning-border);
        color: var(--cw-warning);
    }

    /* Button Styling */
    .btn-cw-primary {
        background: var(--cw-gradient-brand-button);
        border: none;
        color: white;
        padding: 0.875rem 2rem;
        border-radius: 0.75rem;
        font-family: var(--cw-font-heading);
        font-weight: 600;
        font-size: 1rem;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
        box-shadow: var(--cw-shadow-sm);
        width: 100%;
        margin-bottom: 1rem;
    }

    .btn-cw-primary:hover {
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-md);
        color: white;
        text-decoration: none;
    }

    .btn-cw-secondary {
        background: white;
        border: 2px solid var(--cw-brand-primary);
        color: var(--cw-brand-primary);
        padding: 0.875rem 2rem;
        border-radius: 0.75rem;
        font-family: var(--cw-font-heading);
        font-weight: 600;
        font-size: 1rem;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
        width: 100%;
        margin-bottom: 1rem;
    }

    .btn-cw-secondary:hover {
        background: var(--cw-brand-primary);
        color: white;
        transform: translateY(-2px);
        box-shadow: var(--cw-shadow-md);
        text-decoration: none;
    }

    /* Error Steps Card */
    .error-steps-card {
        background: var(--cw-accent-light);
        border: 1px solid var(--cw-brand-accent);
        border-radius: 0.75rem;
        padding: 1.5rem;
        margin: 2rem 0;
    }

    .error-steps-title {
        font-family: var(--cw-font-heading);
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--cw-brand-primary);
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .error-steps-list {
        margin: 0;
        padding-left: 1.5rem;
        color: var(--cw-neutral-700);
    }

    .error-steps-list li {
        margin-bottom: 0.75rem;
        line-height: 1.5;
    }

    .error-steps-list li:last-child {
        margin-bottom: 0;
    }

    /* Utility Classes */
    .d-grid {
        display: grid;
    }

    .text-center {
        text-align: center;
    }

    .mb-4 {
        margin-bottom: 1.5rem;
    }

    .mt-4 {
        margin-top: 1.5rem;
    }

    .pt-4 {
        padding-top: 1.5rem;
    }

    /* Responsive Design */
    @media (max-width: 576px) {
        .email-verify-failed-container {
            padding: 0 1rem;
        }

        .email-verify-failed-header {
            padding: 2rem 1.5rem 1.5rem;
        }

        .email-verify-failed-body {
            padding: 2rem 1.5rem;
        }

        .email-verify-failed-title {
            font-size: 1.875rem;
        }

        .email-verify-failed-icon {
            width: 64px;
            height: 64px;
            font-size: 1.5rem;
        }

        .error-steps-card {
            padding: 1rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<section class="email-verify-failed-section">
    <div class="email-verify-failed-container">
        <div class="email-verify-failed-card">
            <div class="email-verify-failed-header">
                <div class="content">
                    <div class="email-verify-failed-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <h1 class="email-verify-failed-title">Verification Failed</h1>
                    <p class="email-verify-failed-subtitle">We couldn't verify your email address</p>
                </div>
            </div>

            <div class="email-verify-failed-body">
                <!-- Display messages -->
                {% if messages %}
                  {% for message in messages %}
                    <div class="alert-cw alert-cw-error mb-4" role="alert">
                      <i class="fas fa-exclamation-triangle"></i>
                      <span>{{ message }}</span>
                    </div>
                  {% endfor %}
                {% endif %}

                <div class="text-center mb-4">
                  <p style="color: var(--cw-neutral-700); font-size: 1.1rem; line-height: 1.6;">
                    The verification link is invalid or has expired. This can happen if the link is older than 24 hours or has already been used.
                  </p>
                </div>

                <div class="error-steps-card">
                  <h3 class="error-steps-title">
                    <i class="fas fa-lightbulb"></i>What to do next:
                  </h3>
                  <ol class="error-steps-list">
                    <li>Try signing up again with the same email address</li>
                    <li>Check if you already have an account and try logging in instead</li>
                    <li>Check your spam/junk folder for verification emails</li>
                    <li>Make sure you're using the latest verification email</li>
                    <li>Contact our support team if you continue having issues</li>
                  </ol>
                </div>

                <!-- Action Buttons -->
                <div class="d-grid">
                    <a href="{% url 'account_signup' %}" class="btn-cw-primary">
                        <i class="fas fa-user-plus"></i>Try Sign Up Again
                    </a>
                    <a href="{% url 'account_login' %}" class="btn-cw-secondary">
                        <i class="fas fa-sign-in-alt"></i>Already Have Account? Sign In
                    </a>
                </div>

                <!-- Additional Help -->
                <div class="text-center mt-4 pt-4" style="border-top: 1px solid var(--cw-brand-accent);">
                    <p style="color: var(--cw-neutral-600); margin-bottom: 1rem;">
                        <strong>Still having trouble?</strong>
                    </p>
                    <p style="color: var(--cw-neutral-600); margin-bottom: 0;">
                        Need assistance? 
                        <a href="mailto:<EMAIL>" style="color: var(--cw-brand-primary); font-weight: 600; text-decoration: none;">
                            Contact Support
                        </a>
                        or 
                        <a href="{% url 'home_app:home' %}" style="color: var(--cw-brand-primary); font-weight: 600; text-decoration: none;">
                            Return to Home
                        </a>
                    </p>
                </div>

                <!-- Additional Context -->
                {% if token %}
                <div class="mt-4" style="padding: 1rem; background: var(--cw-accent-light); border-radius: 0.5rem; border-left: 4px solid var(--cw-brand-primary);">
                    <p style="color: var(--cw-neutral-600); font-size: 0.875rem; margin-bottom: 0;">
                        <i class="fas fa-info-circle"></i>
                        <strong>Token Information:</strong> If you continue to have issues, please contact support and reference token: 
                        <code style="background: white; padding: 0.25rem 0.5rem; border-radius: 0.25rem;">{{ token|slice:":8" }}...</code>
                    </p>
                </div>
                {% endif %}

                {% if can_resend and user %}
                <div class="mt-4">
                    <div class="alert-cw alert-cw-warning">
                        <i class="fas fa-envelope"></i>
                        <div>
                            <strong>Need a new verification email?</strong><br>
                            <a href="{% url 'accounts_app:resend_verification' %}" style="color: var(--cw-warning); text-decoration: underline;">
                                Click here to request a new verification email
                            </a>
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</section>
{% endblock %} 