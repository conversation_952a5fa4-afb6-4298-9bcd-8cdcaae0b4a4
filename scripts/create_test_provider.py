#!/usr/bin/env python3
"""
Create test provider user script for CozyWish project.
This script creates a test service provider user for testing navbar functionality.
"""

import os
import sys
import django

def create_test_provider():
    """Create a test service provider user."""
    # Add project root to Python path
    project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    sys.path.insert(0, project_root)
    
    # Set up Django
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project_root.settings.development')
    django.setup()
    
    from accounts_app.models import CustomUser, ServiceProviderProfile
    from accounts_app.constants import UserRoles, UserStatus
    
    email = '<EMAIL>'
    password = 'testpass123'
    
    try:
        # Check if user already exists
        try:
            user = CustomUser.objects.get(email=email)
            print('ℹ️  Test provider user already exists - updating settings!')
            # Update existing user
            user.role = UserRoles.SERVICE_PROVIDER
            user.status = UserStatus.ACTIVE
            user.email_verified = True
            user.is_active = True
            user.set_password(password)
            user.save()
        except CustomUser.DoesNotExist:
            # Create new user
            user = CustomUser.objects.create_user(
                email=email,
                password=password,
                role=UserRoles.SERVICE_PROVIDER,
                status=UserStatus.ACTIVE,
                email_verified=True,
                is_active=True
            )
            print('✅ Created test provider user!')
        
        # Create or get provider profile
        profile, profile_created = ServiceProviderProfile.objects.get_or_create(
            user=user,
            defaults={
                'legal_name': 'Test Provider Business',
                'contact_name': 'Test Provider',
                'description': 'Test provider for testing navbar functionality'
            }
        )
        
        if profile_created:
            print('✅ Created provider profile!')
        else:
            print('ℹ️  Provider profile already exists!')
        
        print(f'\n📧 Email: {email}')
        print(f'🔒 Password: {password}')
        print(f'👤 Role: {user.role}')
        print(f'✅ Is service provider: {user.is_service_provider}')
        print(f'📈 Status: {user.status}')
        print(f'📧 Email verified: {user.email_verified}')
        print(f'\n🌐 Login URL: http://127.0.0.1:8000/accounts/provider/login/')
        print(f'🔧 Debug URL: http://127.0.0.1:8000/debug-user/')
        
    except Exception as e:
        print(f'❌ Failed to create test provider: {e}')
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == '__main__':
    create_test_provider() 