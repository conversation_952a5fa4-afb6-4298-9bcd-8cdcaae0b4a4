"""Home page view for the CozyWish application."""

# --- Standard Library Imports ---
import logging

# --- Third-Party Imports ---
from django.contrib.auth import get_user_model
from django.shortcuts import render
from django.http import HttpResponse

# Get the custom user model
User = get_user_model()

# Set up logging
logger = logging.getLogger(__name__)


def home_view(request):
    """
    Home page view that shows appropriate content based on user authentication status.
    """
    return render(request, 'home_app/home.html')

def for_business_view(request):
    """
    Business landing page for service providers.
    """
    return render(request, 'home_app/business_landing.html') 