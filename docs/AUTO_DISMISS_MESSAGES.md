# Auto-Dismiss Messages System for CozyWish

## Overview

The auto-dismiss messages system automatically hides Django messages and custom alerts after a specified duration, providing a better user experience by keeping the interface clean and uncluttered.

## Features

- ✅ **Automatic dismissal** after 3 seconds (configurable)
- ✅ **Hover to pause** - Messages pause when user hovers over them
- ✅ **Visual progress bar** - Shows remaining time before dismissal
- ✅ **Smooth animations** - Fade out and slide animations
- ✅ **Multiple message types** - Support for success, info, warning, error
- ✅ **Custom CozyWish alerts** - Works with `alert-cw-*` classes
- ✅ **Dynamic messages** - Handles messages added via AJAX
- ✅ **Responsive design** - Mobile-friendly
- ✅ **Accessibility support** - ARIA live regions and keyboard navigation

## Installation

The system is automatically included in the base template and works out of the box.

### Files Added:
- `static/js/auto_dismiss_messages.js` - Main functionality
- `static/css/auto_dismiss_messages.css` - Enhanced styling
- `static/js/message_config.js` - Configuration options
- `templates/base.html` - Updated to include scripts and global messages

## Default Timing

| Message Type | Duration |
|-------------|----------|
| Success     | 3 seconds |
| Info        | 3 seconds |
| Warning     | 4 seconds |
| Error       | 5 seconds |
| General     | 3 seconds |

## Usage

### Basic Django Messages

```python
from django.contrib import messages

# These will auto-dismiss after 3 seconds
messages.success(request, "Your email has been verified successfully!")
messages.info(request, "Account created! Please check your email.")
messages.warning(request, "Password will expire in 7 days.")
messages.error(request, "Invalid login credentials.")
```

### Custom CozyWish Alerts in Templates

```html
<!-- These will also auto-dismiss -->
<div class="alert-cw alert-cw-success">
    <i class="fas fa-check-circle me-2"></i>
    Operation completed successfully!
</div>

<div class="alert-cw alert-cw-error">
    <i class="fas fa-exclamation-triangle me-2"></i>
    An error occurred. Please try again.
</div>
```

### Manual Control

```javascript
// Dismiss all messages immediately
dismissAllMessages();

// Pause auto-dismiss for all messages
pauseAutoDismiss();

// Resume auto-dismiss for all messages
resumeAutoDismiss();

// Access the main instance
window.autoDismissMessages.dismissAllMessages();
```

## Customization

### 1. Timing Configuration

Edit `static/js/message_config.js`:

```javascript
window.CozyWishMessageConfig = {
    timing: {
        success: 2000,    // 2 seconds
        info: 4000,       // 4 seconds
        warning: 5000,    // 5 seconds
        error: 7000,      // 7 seconds
        default: 3000     // 3 seconds
    }
};
```

### 2. Page-Specific Overrides

```javascript
pageOverrides: {
    '/accounts/': {
        timing: {
            success: 4000,  // Longer for account pages
            error: 6000
        }
    }
}
```

### 3. Disable for Specific Messages

Add the class `no-auto-dismiss` to prevent auto-dismissal:

```html
<div class="alert alert-warning no-auto-dismiss">
    This message will not auto-dismiss
</div>
```

### 4. Custom Styling

Override CSS in your custom stylesheets:

```css
/* Custom styling for success messages */
.alert-success {
    background: linear-gradient(135deg, #your-color 0%, #your-color-2 100%);
    border-left-color: #your-brand-color;
}

/* Disable progress bar */
.auto-dismiss-progress {
    display: none;
}
```

## Message Types and Classes

### Standard Bootstrap Alerts
- `.alert-success` - Green success messages
- `.alert-info` - Blue info messages
- `.alert-warning` - Yellow warning messages
- `.alert-danger` - Red error messages

### Custom CozyWish Alerts
- `.alert-cw-success` - Custom success styling
- `.alert-cw-info` - Custom info styling
- `.alert-cw-warning` - Custom warning styling
- `.alert-cw-error` - Custom error styling

## Behavior Details

### Hover Interaction
- When user hovers over a message, auto-dismiss is paused
- Progress bar stops animating
- When user stops hovering, countdown resumes from where it left off

### Mobile Responsiveness
- Messages adapt to smaller screens
- Touch-friendly close buttons
- Appropriate spacing and sizing

### Accessibility
- ARIA live regions announce new messages to screen readers
- Keyboard navigation support
- High contrast colors for readability
- Focus indicators for close buttons

## Browser Support

- ✅ Chrome 60+
- ✅ Firefox 60+
- ✅ Safari 12+
- ✅ Edge 79+
- ✅ iOS Safari 12+
- ✅ Android Chrome 60+

## Troubleshooting

### Messages Not Auto-Dismissing

1. **Check console for errors** - Open browser dev tools
2. **Verify script loading** - Ensure `auto_dismiss_messages.js` loads
3. **Check CSS conflicts** - Ensure no CSS is overriding the classes
4. **JavaScript conflicts** - Check for other scripts interfering

### Performance Issues

1. **Too many messages** - System handles up to 5 visible messages
2. **Animation lag** - Reduce animation duration in config
3. **Memory usage** - Old messages are properly cleaned up

### Customization Not Working

1. **Cache issues** - Clear browser cache and Django static files cache
2. **Load order** - Ensure config loads before main script
3. **Syntax errors** - Check console for JavaScript errors

## Advanced Features

### Toast-Style Messages

Add the class `toast-style` to message container for fixed positioning:

```html
<div class="messages-container toast-style">
    <!-- Messages appear as toasts in corner -->
</div>
```

### Sound Notifications

Extend the system to play sounds (future enhancement):

```javascript
// In message_config.js
special: {
    emailVerification: {
        duration: 4000,
        sound: '/static/sounds/success.mp3'
    }
}
```

### Integration with AJAX

The system automatically detects and processes messages added via AJAX:

```javascript
// After AJAX call that adds messages
fetch('/api/some-action/')
    .then(response => response.json())
    .then(data => {
        // Add message to DOM
        document.querySelector('.messages-container').innerHTML += 
            `<div class="alert alert-success">${data.message}</div>`;
        // System automatically detects and processes it
    });
```

## Support

For issues or feature requests related to the auto-dismiss messages system, check:

1. Browser console for error messages
2. Network tab to ensure scripts are loading
3. Django logs for any server-side message issues

## Future Enhancements

- Sound notifications
- Message queuing system
- Analytics tracking
- A/B testing integration
- Custom themes
- Message persistence across page loads 