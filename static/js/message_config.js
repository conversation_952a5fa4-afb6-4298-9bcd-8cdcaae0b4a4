/**
 * Message Configuration for CozyWish
 * 
 * Customize auto-dismiss behavior and timing here
 */

window.CozyWishMessageConfig = {
    // Default timing (in milliseconds)
    timing: {
        success: 3000,    // 3 seconds for success messages
        info: 3000,       // 3 seconds for info messages
        warning: 4000,    // 4 seconds for warnings
        error: 5000,      // 5 seconds for errors
        default: 3000     // 3 seconds for general messages
    },
    
    // Animation settings
    animation: {
        fadeOut: 500,     // Fade out duration in ms
        slideIn: 300,     // Slide in duration in ms
        progressBar: true // Show progress bar
    },
    
    // Behavior settings
    behavior: {
        pauseOnHover: true,      // Pause auto-dismiss when hovering
        closeOnClick: false,     // Close when clicking the message (not just the X)
        stackMessages: true,     // Stack multiple messages
        maxVisible: 5,           // Maximum messages visible at once
        globalPosition: 'top'    // 'top', 'bottom', or 'fixed-top'
    },
    
    // Special message types
    special: {
        emailVerification: {
            duration: 4000,
            emphasize: true,
            sound: false  // Could add sound notification
        },
        accountCreated: {
            duration: 4000,
            emphasize: true
        }
    },
    
    // Override for specific pages
    pageOverrides: {
        '/accounts/': {
            timing: {
                success: 4000,  // Slightly longer for account pages
                error: 6000
            }
        },
        '/admin/': {
            timing: {
                default: 5000   // Longer for admin pages
            }
        }
    }
};

/**
 * Apply configuration to auto-dismiss system
 */
function applyMessageConfig() {
    if (window.autoDismissMessages) {
        const config = window.CozyWishMessageConfig;
        const currentPath = window.location.pathname;
        
        // Apply page-specific overrides
        let finalConfig = { ...config };
        for (const path in config.pageOverrides) {
            if (currentPath.startsWith(path)) {
                finalConfig = {
                    ...finalConfig,
                    timing: {
                        ...finalConfig.timing,
                        ...config.pageOverrides[path].timing
                    }
                };
                break;
            }
        }
        
        // Update the auto-dismiss system with new config
        window.autoDismissMessages.updateConfig?.(finalConfig);
    }
}

// Apply configuration when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', applyMessageConfig);
} else {
    applyMessageConfig();
} 