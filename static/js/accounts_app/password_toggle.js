/**
 * CozyWish Password Toggle Functionality
 * 
 * Provides reusable password show/hide toggle functionality with accessibility support.
 * 
 * Usage:
 * 1. Add a password input field with an ID
 * 2. Add a toggle button with class "toggle-password" and data-target attribute pointing to the input ID
 * 3. Include this script in your template
 * 
 * Example HTML:
 * <div class="input-group">
 *   <input type="password" id="password" class="form-control">
 *   <button type="button" class="btn toggle-password" data-target="#password">
 *     <i class="fas fa-eye"></i>
 *   </button>
 * </div>
 */

(function() {
    'use strict';

    /**
     * Toggle password visibility for a specific input field
     * @param {HTMLElement} toggleBtn - The toggle button element
     */
    function togglePassword(toggleBtn) {
        const targetSelector = toggleBtn.getAttribute('data-target');
        if (!targetSelector) {
            console.error('CozyWish Password Toggle: No data-target attribute found on toggle button');
            return;
        }

        const input = document.querySelector(targetSelector);
        if (!input) {
            console.error('CozyWish Password Toggle: Target input not found:', targetSelector);
            return;
        }

        // Toggle password visibility
        const isPassword = input.type === 'password';
        input.type = isPassword ? 'text' : 'password';

        // Update icon
        const icon = toggleBtn.querySelector('i');
        if (icon) {
            if (isPassword) {
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        }

        // Update accessibility attributes
        const newTitle = isPassword ? 'Hide password' : 'Show password';
        toggleBtn.title = newTitle;
        toggleBtn.setAttribute('aria-label', 'Toggle password visibility: ' + (isPassword ? 'currently shown' : 'currently hidden'));

        // Add visual feedback
        toggleBtn.classList.toggle('password-visible', !isPassword);

        // Dispatch custom event for external listeners
        const event = new CustomEvent('passwordToggled', {
            detail: {
                input: input,
                toggleBtn: toggleBtn,
                isVisible: !isPassword
            },
            bubbles: true
        });
        toggleBtn.dispatchEvent(event);
    }

    /**
     * Initialize password toggle functionality
     */
    function initializePasswordToggle() {
        // Click event handler
        document.addEventListener('click', function(e) {
            const toggleBtn = e.target.closest('.toggle-password');
            if (toggleBtn) {
                e.preventDefault();
                e.stopPropagation();
                togglePassword(toggleBtn);
            }
        });

        // Keyboard event handler for accessibility
        document.addEventListener('keydown', function(e) {
            const toggleBtn = e.target.closest('.toggle-password');
            if (toggleBtn && (e.key === 'Enter' || e.key === ' ' || e.key === 'Spacebar')) {
                e.preventDefault();
                e.stopPropagation();
                togglePassword(toggleBtn);
            }
        });

        // Initialize existing toggle buttons
        const toggleButtons = document.querySelectorAll('.toggle-password');
        toggleButtons.forEach(function(btn) {
            // Ensure button has proper attributes
            if (!btn.hasAttribute('tabindex')) {
                btn.setAttribute('tabindex', '0');
            }
            
            // Add role for screen readers
            btn.setAttribute('role', 'button');
            
            // Ensure aria-label is set
            if (!btn.hasAttribute('aria-label')) {
                btn.setAttribute('aria-label', 'Toggle password visibility');
            }

            // Ensure proper title is set
            if (!btn.hasAttribute('title')) {
                btn.setAttribute('title', 'Show password');
            }

            // Validate data-target attribute
            const targetSelector = btn.getAttribute('data-target');
            if (!targetSelector) {
                console.warn('CozyWish Password Toggle: Toggle button found without data-target attribute:', btn);
            } else {
                const targetInput = document.querySelector(targetSelector);
                if (!targetInput) {
                    console.warn('CozyWish Password Toggle: Target input not found for selector:', targetSelector);
                }
            }
        });

        console.log('CozyWish Password Toggle: Initialized', toggleButtons.length, 'toggle buttons');
    }

    /**
     * Add a password toggle to an existing input field dynamically
     * @param {string|HTMLElement} inputSelector - CSS selector or input element
     * @param {Object} options - Configuration options
     */
    function addPasswordToggle(inputSelector, options = {}) {
        const input = typeof inputSelector === 'string' ? document.querySelector(inputSelector) : inputSelector;
        if (!input) {
            console.error('CozyWish Password Toggle: Input not found:', inputSelector);
            return;
        }

        // Default options
        const config = {
            buttonClass: 'btn toggle-password',
            iconClass: 'fas fa-eye',
            position: 'after', // 'after' or 'before'
            ...options
        };

        // Create toggle button
        const toggleBtn = document.createElement('button');
        toggleBtn.type = 'button';
        toggleBtn.className = config.buttonClass;
        toggleBtn.setAttribute('data-target', '#' + input.id);
        toggleBtn.setAttribute('title', 'Show password');
        toggleBtn.setAttribute('aria-label', 'Toggle password visibility');
        toggleBtn.innerHTML = `<i class="${config.iconClass}" aria-hidden="true"></i>`;

        // Wrap input in input-group if not already wrapped
        let inputGroup = input.closest('.input-group');
        if (!inputGroup) {
            inputGroup = document.createElement('div');
            inputGroup.className = 'input-group';
            input.parentNode.insertBefore(inputGroup, input);
            inputGroup.appendChild(input);
        }

        // Add toggle button
        if (config.position === 'before') {
            inputGroup.insertBefore(toggleBtn, input);
        } else {
            inputGroup.appendChild(toggleBtn);
        }

        return toggleBtn;
    }

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializePasswordToggle);
    } else {
        initializePasswordToggle();
    }

    // Expose utility function globally
    window.CozyWishPasswordToggle = {
        toggle: togglePassword,
        add: addPasswordToggle,
        init: initializePasswordToggle
    };

})(); 