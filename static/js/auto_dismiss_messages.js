/**
 * Auto-Dismiss Messages System for CozyWish
 * 
 * This module handles automatic dismissal of Django messages and custom alerts
 * after a specified duration. It works with all message types and alert styles
 * used throughout the application.
 */

class AutoDismissMessages {
    constructor(options = {}) {
        this.defaultDuration = options.duration || 3000; // 3 seconds default
        this.successDuration = options.successDuration || 3000;
        this.infoDuration = options.infoDuration || 3000;
        this.warningDuration = options.warningDuration || 4000; // Slightly longer for warnings
        this.errorDuration = options.errorDuration || 5000; // Longer for errors
        this.fadeOutDuration = options.fadeOutDuration || 500; // Fade animation duration
        
        this.init();
    }

    /**
     * Initialize the auto-dismiss system
     */
    init() {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.processMessages());
        } else {
            this.processMessages();
        }

        // Also process messages that might be added dynamically
        this.observeNewMessages();
    }

    /**
     * Process all existing messages on the page
     */
    processMessages() {
        // Standard Django alerts with Bootstrap classes
        const bootstrapAlerts = document.querySelectorAll('.alert:not(.alert-dismissible-processed)');
        bootstrapAlerts.forEach(alert => this.processBootstrapAlert(alert));

        // Custom CozyWish alerts
        const customAlerts = document.querySelectorAll('[class*="alert-cw"]:not(.alert-dismissible-processed)');
        customAlerts.forEach(alert => this.processCustomAlert(alert));

        // Messages from Django messages framework specifically
        const messageContainers = document.querySelectorAll('.messages-container .alert:not(.alert-dismissible-processed)');
        messageContainers.forEach(alert => this.processBootstrapAlert(alert));
    }

    /**
     * Process Bootstrap-style alerts (standard Django messages)
     */
    processBootstrapAlert(alert) {
        if (alert.classList.contains('alert-dismissible-processed')) return;
        
        alert.classList.add('alert-dismissible-processed');
        
        // Determine message type and duration
        let duration = this.defaultDuration;
        if (alert.classList.contains('alert-success')) {
            duration = this.successDuration;
        } else if (alert.classList.contains('alert-info')) {
            duration = this.infoDuration;
        } else if (alert.classList.contains('alert-warning')) {
            duration = this.warningDuration;
        } else if (alert.classList.contains('alert-danger') || alert.classList.contains('alert-error')) {
            duration = this.errorDuration;
        }

        // Add hover listeners to pause/resume auto-dismiss
        this.addHoverListeners(alert, duration);

        // Schedule auto-dismiss
        this.scheduleAutoDismiss(alert, duration);
    }

    /**
     * Process custom CozyWish alerts
     */
    processCustomAlert(alert) {
        if (alert.classList.contains('alert-dismissible-processed')) return;
        
        alert.classList.add('alert-dismissible-processed');
        
        // Determine message type and duration from custom classes
        let duration = this.defaultDuration;
        if (alert.classList.contains('alert-cw-success')) {
            duration = this.successDuration;
        } else if (alert.classList.contains('alert-cw-info')) {
            duration = this.infoDuration;
        } else if (alert.classList.contains('alert-cw-warning')) {
            duration = this.warningDuration;
        } else if (alert.classList.contains('alert-cw-error') || alert.classList.contains('alert-cw-danger')) {
            duration = this.errorDuration;
        }

        // Add hover listeners to pause/resume auto-dismiss
        this.addHoverListeners(alert, duration);

        // Schedule auto-dismiss
        this.scheduleAutoDismiss(alert, duration);
    }

    /**
     * Add hover listeners to pause auto-dismiss on hover
     */
    addHoverListeners(alert, originalDuration) {
        let timeoutId = null;
        let remainingTime = originalDuration;
        let startTime = Date.now();

        // Store timeout reference on the element
        alert._autoDismissTimeout = null;
        alert._remainingTime = remainingTime;
        alert._startTime = startTime;

        alert.addEventListener('mouseenter', () => {
            if (alert._autoDismissTimeout) {
                clearTimeout(alert._autoDismissTimeout);
                alert._remainingTime = Math.max(0, remainingTime - (Date.now() - alert._startTime));
            }
        });

        alert.addEventListener('mouseleave', () => {
            if (alert._remainingTime > 0) {
                alert._startTime = Date.now();
                this.scheduleAutoDismiss(alert, alert._remainingTime);
            }
        });
    }

    /**
     * Schedule the auto-dismiss timeout
     */
    scheduleAutoDismiss(alert, duration) {
        alert._autoDismissTimeout = setTimeout(() => {
            this.dismissAlert(alert);
        }, duration);
    }

    /**
     * Dismiss an alert with smooth animation
     */
    dismissAlert(alert) {
        // Add progress indicator if wanted (optional)
        this.addDismissAnimation(alert);

        // Use Bootstrap's built-in dismiss if available
        const closeButton = alert.querySelector('.btn-close, [data-bs-dismiss="alert"]');
        if (closeButton) {
            closeButton.click();
        } else {
            // Custom dismiss animation
            this.customDismissAnimation(alert);
        }
    }

    /**
     * Add visual progress indicator (optional enhancement)
     */
    addDismissAnimation(alert) {
        // Add a subtle progress bar at the bottom of the alert
        if (!alert.querySelector('.auto-dismiss-progress')) {
            const progressBar = document.createElement('div');
            progressBar.className = 'auto-dismiss-progress';
            progressBar.style.cssText = `
                position: absolute;
                bottom: 0;
                left: 0;
                height: 2px;
                background-color: rgba(0,0,0,0.2);
                width: 100%;
                transform-origin: left;
                animation: autoDismissProgress 3s linear;
            `;
            
            // Add CSS animation if not already present
            if (!document.querySelector('#auto-dismiss-styles')) {
                const style = document.createElement('style');
                style.id = 'auto-dismiss-styles';
                style.textContent = `
                    @keyframes autoDismissProgress {
                        from { transform: scaleX(1); }
                        to { transform: scaleX(0); }
                    }
                    .alert { position: relative; }
                `;
                document.head.appendChild(style);
            }
            
            alert.appendChild(progressBar);
        }
    }

    /**
     * Custom dismiss animation for alerts without Bootstrap
     */
    customDismissAnimation(alert) {
        alert.style.transition = `all ${this.fadeOutDuration}ms ease-out`;
        alert.style.opacity = '0';
        alert.style.transform = 'translateY(-10px)';
        alert.style.maxHeight = '0';
        alert.style.padding = '0';
        alert.style.margin = '0';
        alert.style.overflow = 'hidden';

        setTimeout(() => {
            if (alert.parentNode) {
                alert.parentNode.removeChild(alert);
            }
        }, this.fadeOutDuration);
    }

    /**
     * Observe for new messages added dynamically
     */
    observeNewMessages() {
        if (typeof MutationObserver !== 'undefined') {
            const observer = new MutationObserver((mutations) => {
                mutations.forEach((mutation) => {
                    mutation.addedNodes.forEach((node) => {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            // Check if the added node is an alert
                            if (this.isAlert(node)) {
                                setTimeout(() => this.processNewAlert(node), 100);
                            }
                            
                            // Check for alerts within the added node
                            const alerts = node.querySelectorAll?.('.alert, [class*="alert-cw"]');
                            if (alerts) {
                                alerts.forEach(alert => {
                                    setTimeout(() => this.processNewAlert(alert), 100);
                                });
                            }
                        }
                    });
                });
            });

            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
        }
    }

    /**
     * Check if an element is an alert
     */
    isAlert(element) {
        return element.classList && (
            element.classList.contains('alert') ||
            Array.from(element.classList).some(className => className.startsWith('alert-cw'))
        );
    }

    /**
     * Process a newly added alert
     */
    processNewAlert(alert) {
        if (alert.classList.contains('alert-dismissible-processed')) return;

        if (this.isAlert(alert)) {
            if (alert.classList.contains('alert') || Array.from(alert.classList).some(c => c.startsWith('alert-cw'))) {
                if (Array.from(alert.classList).some(c => c.startsWith('alert-cw'))) {
                    this.processCustomAlert(alert);
                } else {
                    this.processBootstrapAlert(alert);
                }
            }
        }
    }

    /**
     * Manual method to dismiss all messages immediately
     */
    dismissAllMessages() {
        const allAlerts = document.querySelectorAll('.alert, [class*="alert-cw"]');
        allAlerts.forEach(alert => {
            if (alert._autoDismissTimeout) {
                clearTimeout(alert._autoDismissTimeout);
            }
            this.dismissAlert(alert);
        });
    }

    /**
     * Pause auto-dismiss for all messages
     */
    pauseAutoDismiss() {
        const allAlerts = document.querySelectorAll('.alert, [class*="alert-cw"]');
        allAlerts.forEach(alert => {
            if (alert._autoDismissTimeout) {
                clearTimeout(alert._autoDismissTimeout);
                alert._isPaused = true;
            }
        });
    }

    /**
     * Resume auto-dismiss for all messages
     */
    resumeAutoDismiss() {
        const allAlerts = document.querySelectorAll('.alert, [class*="alert-cw"]');
        allAlerts.forEach(alert => {
            if (alert._isPaused && alert._remainingTime > 0) {
                alert._isPaused = false;
                alert._startTime = Date.now();
                this.scheduleAutoDismiss(alert, alert._remainingTime);
            }
        });
    }
}

// Initialize the auto-dismiss system with default settings
// Can be customized by passing options
const autoDismissMessages = new AutoDismissMessages({
    duration: 3000,          // 3 seconds for general messages
    successDuration: 3000,   // 3 seconds for success messages
    infoDuration: 3000,      // 3 seconds for info messages  
    warningDuration: 4000,   // 4 seconds for warnings
    errorDuration: 5000,     // 5 seconds for errors
    fadeOutDuration: 500     // 0.5 seconds for fade animation
});

// Export for manual control if needed
window.autoDismissMessages = autoDismissMessages;

// Also provide manual functions for convenience
window.dismissAllMessages = () => autoDismissMessages.dismissAllMessages();
window.pauseAutoDismiss = () => autoDismissMessages.pauseAutoDismiss();
window.resumeAutoDismiss = () => autoDismissMessages.resumeAutoDismiss(); 