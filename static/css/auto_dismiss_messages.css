/**
 * Auto-Dismiss Messages Styling for CozyWish
 * 
 * Enhanced styling for Django messages with auto-dismiss functionality
 */

/* Global messages container styling */
.messages-container {
    position: relative;
    z-index: 1050;
    margin-bottom: 1rem;
}

/* Enhanced alert styling */
.alert {
    position: relative;
    border: none;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    margin-bottom: 1rem;
    overflow: hidden;
    transition: all 0.3s ease;
}

.alert:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

/* Success messages */
.alert-success {
    background: linear-gradient(135deg, #D4EDDA 0%, #C3E6CB 100%);
    color: #155724;
    border-left: 4px solid #28A745;
}

.alert-success .fas {
    color: #28A745;
}

/* Info messages */
.alert-info {
    background: linear-gradient(135deg, #CCE7FF 0%, #B8DAFF 100%);
    color: #004085;
    border-left: 4px solid #007BFF;
}

.alert-info .fas {
    color: #007BFF;
}

/* Warning messages */
.alert-warning {
    background: linear-gradient(135deg, #FFF3CD 0%, #FFEAA7 100%);
    color: #856404;
    border-left: 4px solid #FFC107;
}

.alert-warning .fas {
    color: #FFC107;
}

/* Error/danger messages */
.alert-danger,
.alert-error {
    background: linear-gradient(135deg, #F8D7DA 0%, #F1B9B7 100%);
    color: #721C24;
    border-left: 4px solid #DC3545;
}

.alert-danger .fas,
.alert-error .fas {
    color: #DC3545;
}

/* Custom CozyWish alerts */
.alert-cw-success {
    background: linear-gradient(135deg, #F0FDF4 0%, #DCFCE7 100%);
    color: #166534;
    border: 2px solid #22C55E;
    border-radius: 12px;
    padding: 1rem 1.25rem;
}

.alert-cw-info {
    background: linear-gradient(135deg, #EFF6FF 0%, #DBEAFE 100%);
    color: #1E40AF;
    border: 2px solid #3B82F6;
    border-radius: 12px;
    padding: 1rem 1.25rem;
}

.alert-cw-warning {
    background: linear-gradient(135deg, #FFFBEB 0%, #FEF3C7 100%);
    color: #92400E;
    border: 2px solid #F59E0B;
    border-radius: 12px;
    padding: 1rem 1.25rem;
}

.alert-cw-error {
    background: linear-gradient(135deg, #FEF2F2 0%, #FECACA 100%);
    color: #991B1B;
    border: 2px solid #EF4444;
    border-radius: 12px;
    padding: 1rem 1.25rem;
}

/* Progress bar for auto-dismiss indication */
.auto-dismiss-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 0 0 12px 12px;
    transform-origin: left;
}

/* Animations */
@keyframes autoDismissProgress {
    from { 
        transform: scaleX(1); 
        opacity: 0.6;
    }
    to { 
        transform: scaleX(0); 
        opacity: 0.2;
    }
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-100%);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideOutUp {
    from {
        opacity: 1;
        transform: translateY(0);
    }
    to {
        opacity: 0;
        transform: translateY(-100%);
    }
}

/* Apply slide-in animation to new alerts */
.alert.fade.show {
    animation: slideInDown 0.3s ease-out;
}

/* Enhanced close button */
.alert .btn-close {
    background: none;
    border: none;
    opacity: 0.6;
    transition: opacity 0.2s ease;
    margin-left: auto;
}

.alert .btn-close:hover {
    opacity: 1;
}

/* Responsive design */
@media (max-width: 768px) {
    .messages-container {
        margin: 0 0.5rem 1rem;
    }
    
    .alert {
        border-radius: 8px;
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
    }
}

/* Fixed position messages for critical alerts (optional) */
.messages-container.fixed-top {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    width: 90%;
    max-width: 600px;
    z-index: 9999;
}

/* Hover pause indicator */
.alert:hover::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.1);
    pointer-events: none;
    border-radius: inherit;
}

/* Message type icons */
.alert .fas {
    font-size: 1.1em;
    margin-right: 0.5rem;
    vertical-align: middle;
}

/* Accessibility improvements */
.alert[role="alert"] {
    position: relative;
}

.alert:focus-within {
    outline: 2px solid #4285F4;
    outline-offset: 2px;
}

/* Stack multiple messages nicely */
.messages-container .alert + .alert {
    margin-top: 0.5rem;
}

/* Special styling for email verification messages */
.alert:has-text("email has been verified"),
.alert:has-text("check your email") {
    border-left-color: #42241A;
}

.alert:has-text("email has been verified") {
    background: linear-gradient(135deg, #F8F4F0 0%, #F0E6D7 100%);
    color: #42241A;
}

/* Toast-style messages (alternative layout) */
.messages-container.toast-style {
    position: fixed;
    top: 20px;
    right: 20px;
    width: 400px;
    z-index: 9999;
}

.messages-container.toast-style .alert {
    margin-bottom: 0.5rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(8px);
}

/* Dark mode support (if implemented) */
@media (prefers-color-scheme: dark) {
    .alert-success {
        background: linear-gradient(135deg, #1B4332 0%, #2D5A41 100%);
        color: #A7F3D0;
    }
    
    .alert-info {
        background: linear-gradient(135deg, #1E3A8A 0%, #1E40AF 100%);
        color: #BFDBFE;
    }
    
    .alert-warning {
        background: linear-gradient(135deg, #92400E 0%, #B45309 100%);
        color: #FDE68A;
    }
    
    .alert-danger,
    .alert-error {
        background: linear-gradient(135deg, #7F1D1D 0%, #991B1B 100%);
        color: #FECACA;
    }
} 